# 🎵 LX Music 自定义源优化说明

## 📋 版本信息

- **脚本名称**: 偷听音乐源(官方规范版)
- **版本**: 2.0.0
- **作者**: Claude 4.0 sonnet
- **基于**: 落雪音乐官方文档规范

## 🚀 主要优化内容

### **1. 符合官方规范**
- ✅ 基于落雪音乐官方文档重构
- ✅ 使用官方推荐的API结构
- ✅ 正确的事件处理机制
- ✅ 标准的初始化流程

### **2. 环境检测优化**
```javascript
// 使用官方API进行环境检测
const envInfo = utils_custom.getEnvironmentInfo()
// 支持: desktop, mobile 等环境
```

### **3. 音质映射优化**
```javascript
QUALITY_MAP: {
  '128k': 'standard',      // 标准音质
  '320k': 'lossless',      // 320k映射到无损音质 ⬆️
  'flac': 'hires',         // 无损映射到Hi-Res音质 ⬆️⬆️
  'flac24bit': 'jymaster'  // 超清母带 ⭐
}
```

### **4. 错误处理增强**
- ✅ 安全的JSON解析
- ✅ 详细的错误日志
- ✅ 多种歌曲ID格式支持
- ✅ 网络请求超时处理

### **5. 请求头优化**
```javascript
headers: {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'User-Agent': `LX-Music-Desktop/${lxVersion}`,
  'X-LX-Platform': envInfo.platform,
  'Cache-Control': 'no-cache'
}
```

### **6. 重试机制**
- ✅ 自动重试失败的请求
- ✅ 递增延迟策略
- ✅ 可配置的重试次数

## 🔧 配置说明

### **API配置**
```javascript
const CONFIG = {
  API_BASE_URL: 'https://suwyy.deno.dev/api',
  REQUEST_TIMEOUT: 15000,
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000
}
```

### **支持的音质**
- `128k` - 标准音质
- `320k` - 高品质音质 (映射到无损)
- `flac` - 无损音质 (映射到Hi-Res)
- `flac24bit` - 超高清音质 (映射到母带)

## 📊 功能特性

### **多平台支持**
- 🖥️ 桌面版 LX Music
- 📱 移动版 LX Music
- 🌐 Web版 LX Music

### **歌曲ID识别**
支持多种歌曲ID格式：
- `songmid` - QQ音乐ID
- `id` - 通用ID
- `songId` - 歌曲ID
- `rid` - 资源ID
- `hash` - 哈希值
- `songid` - 歌曲标识

### **智能错误处理**
- 🔄 自动重试机制
- 📝 详细错误日志
- 🛡️ 安全的数据解析
- ⏱️ 请求超时保护

## 🎯 使用方法

### **1. 安装脚本**
1. 打开落雪音乐
2. 进入设置 → 自定义源
3. 添加新的自定义源
4. 粘贴脚本内容
5. 保存并启用

### **2. 验证安装**
查看控制台日志，应该看到：
```
[偷听音源] LX Music API版本: x.x.x
[偷听音源] 运行环境: desktop
[偷听音源] 脚本版本: 2.0.0
[偷听音源] ✅ 初始化完成
[偷听音源] 🎵 准备就绪，等待音乐请求...
```

### **3. 测试功能**
1. 搜索歌曲
2. 选择不同音质
3. 播放测试
4. 查看控制台日志确认工作状态

## 🔍 调试信息

### **日志级别**
- 📨 请求信息
- 🎵 成功响应
- ❌ 错误信息
- ⚠️ 警告信息
- 🔄 重试信息

### **常见问题**

#### **1. 无法获取歌曲链接**
- 检查网络连接
- 确认API服务状态
- 查看错误日志

#### **2. 音质不符合预期**
- 检查音质映射配置
- 确认API支持的音质
- 查看实际返回的音质

#### **3. 请求超时**
- 检查网络速度
- 调整超时时间配置
- 启用重试机制

## 📈 性能优化

### **1. 请求优化**
- ✅ 合理的超时时间
- ✅ 智能重试机制
- ✅ 错误快速失败

### **2. 内存优化**
- ✅ 及时释放资源
- ✅ 避免内存泄漏
- ✅ 高效的数据处理

### **3. 网络优化**
- ✅ 压缩请求数据
- ✅ 缓存控制
- ✅ 连接复用

## 🛠️ 自定义配置

### **修改API地址**
```javascript
API_BASE_URL: 'https://your-api-server.com/api'
```

### **调整超时时间**
```javascript
REQUEST_TIMEOUT: 30000 // 30秒
```

### **修改音质映射**
```javascript
QUALITY_MAP: {
  '128k': 'standard',
  '320k': 'high',      // 自定义映射
  'flac': 'lossless',
  'flac24bit': 'hires'
}
```

## 🔄 更新日志

### **v2.0.0 (当前版本)**
- 🆕 基于官方规范重构
- 🆕 添加重试机制
- 🆕 优化错误处理
- 🆕 增强环境检测
- 🆕 改进日志系统

### **v1.1.0**
- 🔧 优化音质映射
- 🔧 分析PC/移动端差异

### **v1.0.0**
- 🎉 初始版本

## 📞 技术支持

如果遇到问题，请：
1. 查看控制台日志
2. 检查网络连接
3. 确认API服务状态
4. 提供详细的错误信息

---

**🎵 享受高品质音乐体验！**
