/**
 * @name 偷听音乐源
 * @description 网易云音乐解析 - 增强版本
 * @version 1.1.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[偷听音源] 增强版本启动')

// HTTP请求封装
const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// 音质映射
const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster'
  }
}

// URL处理函数
function processUrl(url) {
  if (!url) return null
  
  // 确保使用HTTPS
  if (url.startsWith('http://')) {
    url = url.replace('http://', 'https://')
  }
  
  // 添加必要的参数（如果需要）
  if (url.includes('music.126.net') && !url.includes('vuutv=')) {
    // 网易云的URL可能需要特殊处理
    console.log('[偷听音源] 检测到网易云URL，保持原样')
  }
  
  return url
}

// API实现
const apis = {
  wy: {
    musicUrl(musicInfo, quality) {
      console.log('[偷听音源] ========== 开始获取音乐URL ==========')
      console.log('[偷听音源] musicInfo:', JSON.stringify(musicInfo))
      console.log('[偷听音源] quality:', quality)
      
      // 提取歌曲ID - 更全面的提取逻辑
      let songId = null
      if (musicInfo) {
        // 尝试多种可能的ID字段
        const idFields = ['songmid', 'id', 'songId', 'rid', 'hash', 'songid', 'copyrightId']
        for (const field of idFields) {
          if (musicInfo[field]) {
            songId = String(musicInfo[field])
            console.log(`[偷听音源] 从 ${field} 提取ID: ${songId}`)
            break
          }
        }
      }
      
      // 如果还是没有ID，使用默认测试ID
      if (!songId) {
        songId = '1901371647'
        console.log('[偷听音源] 使用默认测试ID:', songId)
      }
      
      console.log('[偷听音源] 最终使用ID:', songId)
      console.log('[偷听音源] 请求音质:', quality)
      
      // 构建请求数据
      const requestData = {
        ids: songId,
        level: quality || 'lossless'
      }
      
      console.log('[偷听音源] 发送请求数据:', JSON.stringify(requestData))
      console.log('[偷听音源] 请求地址: https://suwyy.deno.dev/api/song')
      
      // 发送API请求
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        body: JSON.stringify(requestData),
        timeout: 20000
      }).then(responseBody => {
        console.log('[偷听音源] 收到原始响应:', responseBody)
        
        try {
          // 解析响应
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          console.log('[偷听音源] 解析后数据:', JSON.stringify(data, null, 2))
          
          if (data && data.status === 200 && data.url) {
            console.log('[偷听音源] ✅ API返回成功')
            console.log('[偷听音源] 原始URL:', data.url)
            console.log('[偷听音源] 歌曲信息:', data.name || '未知')
            console.log('[偷听音源] 歌手信息:', data.ar_name || '未知')
            console.log('[偷听音源] 音质信息:', data.level || '未知')
            console.log('[偷听音源] 文件大小:', data.size || '未知')
            console.log('[偷听音源] 比特率:', data.br || '未知')
            
            // 处理URL
            const finalUrl = processUrl(data.url)
            console.log('[偷听音源] 最终URL:', finalUrl)
            
            // 验证URL格式
            if (!finalUrl || !finalUrl.startsWith('http')) {
              throw new Error('无效的URL格式: ' + finalUrl)
            }
            
            console.log('[偷听音源] 🎵 返回播放URL给落雪音乐')
            return finalUrl
            
          } else {
            const error = (data && data.error) || '获取音乐链接失败'
            console.error('[偷听音源] ❌ API错误:', error)
            console.error('[偷听音源] 完整响应:', data)
            throw new Error(error)
          }
        } catch (parseError) {
          console.error('[偷听音源] ❌ 响应解析失败:', parseError.message)
          console.error('[偷听音源] 原始响应:', responseBody)
          throw new Error('响应解析失败: ' + parseError.message)
        }
      }).catch(error => {
        console.error('[偷听音源] ❌ 请求失败:', error.message)
        console.error('[偷听音源] 错误详情:', error)
        throw error
      })
    }
  }
}

// 事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] ========== 收到落雪音乐请求 ==========')
  console.log('[偷听音源] source:', source)
  console.log('[偷听音源] action:', action)
  console.log('[偷听音源] info:', JSON.stringify(info))
  
  switch (action) {
    case 'musicUrl':
      if (apis[source] && apis[source].musicUrl) {
        if (!qualitys[source] || !qualitys[source][info.type]) {
          console.error('[偷听音源] 不支持的音质:', info.type)
          return Promise.reject(new Error('不支持的音质: ' + info.type))
        }
        
        const quality = qualitys[source][info.type]
        console.log('[偷听音源] 音质映射:', info.type, '->', quality)
        
        return apis[source].musicUrl(info.musicInfo, quality)
      } else {
        console.error('[偷听音源] 不支持的源:', source)
        return Promise.reject(new Error('不支持的源: ' + source))
      }
    default:
      console.error('[偷听音源] 不支持的操作:', action)
      return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

// 初始化
console.log('[偷听音源] 开始初始化...')

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源(增强版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[偷听音源] ========== 初始化完成 ==========')
console.log('[偷听音源] 源名称: 偷听音乐源(增强版)')
console.log('[偷听音源] 支持平台: 网易云音乐 (wy)')
console.log('[偷听音源] 支持音质: 128k, 320k, flac, flac24bit')
console.log('[偷听音源] API地址: https://suwyy.deno.dev/api/song')
console.log('[偷听音源] 🎵 准备就绪，等待播放请求...')
