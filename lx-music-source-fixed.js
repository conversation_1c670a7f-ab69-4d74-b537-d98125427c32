/**
 * @name 偷听音乐源(修复版)
 * @description 完全适配落雪音乐的自定义源，修复换源失败问题
 * @version 2.1.0
 * <AUTHOR> 4.0 sonnet
 * @homepage https://github.com/your-repo/lx-music-source
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[偷听音源] 🚀 落雪音乐适配版启动')
console.log('[偷听音源] LX Music版本:', globalThis.lx.version)
console.log('[偷听音源] 运行环境:', globalThis.lx.env)

// 配置
const CONFIG = {
  // 根据您的实际部署情况修改这个URL
  API_BASE_URL: 'http://localhost:3004',  // 本地测试
  // API_BASE_URL: 'https://suwyy.deno.dev',  // 线上地址
  
  // 音质映射 - 根据您的偏好
  QUALITY_MAP: {
    '128k': 'standard',      // 标准音质
    '320k': 'lossless',      // 320k映射到无损 ⬆️
    'flac': 'hires',         // 无损映射到Hi-Res ⬆️⬆️
    'flac24bit': 'jymaster'  // 超清母带 ⭐
  },
  
  REQUEST_TIMEOUT: 15000
}

// 工具函数
function extractSongId(musicInfo) {
  // 支持多种歌曲ID格式
  const possibleIds = [
    musicInfo.songmid,
    musicInfo.id,
    musicInfo.songId,
    musicInfo.rid,
    musicInfo.hash,
    musicInfo.songid
  ]
  
  for (const id of possibleIds) {
    if (id && String(id).trim()) {
      return String(id).trim()
    }
  }
  
  return null
}

function safeJsonParse(str) {
  try {
    return typeof str === 'string' ? JSON.parse(str) : str
  } catch (error) {
    console.error('[偷听音源] JSON解析失败:', error.message)
    return null
  }
}

// 主要功能：获取音乐URL
function getMusicUrl(musicInfo, quality) {
  return new Promise((resolve, reject) => {
    console.log('[偷听音源] 📨 开始获取音乐链接')
    console.log('[偷听音源] 歌曲信息:', musicInfo)
    console.log('[偷听音源] 请求音质:', quality)
    
    // 提取歌曲ID
    const songId = extractSongId(musicInfo)
    if (!songId) {
      const error = '无法提取歌曲ID，支持的字段: songmid, id, songId, rid, hash, songid'
      console.error('[偷听音源] ❌', error)
      return reject(new Error(error))
    }
    
    console.log('[偷听音源] 提取到歌曲ID:', songId)
    
    // 音质映射
    const apiQuality = CONFIG.QUALITY_MAP[quality] || 'lossless'
    console.log('[偷听音源] 音质映射:', quality, '->', apiQuality)
    
    // 构建请求数据 - 根据您的API格式
    const requestData = {
      ids: String(songId),
      level: apiQuality
    }
    
    console.log('[偷听音源] 请求数据:', requestData)
    
    // 发送请求
    request(`${CONFIG.API_BASE_URL}/api/song`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'LX-Music-Desktop'
      },
      body: JSON.stringify(requestData),
      timeout: CONFIG.REQUEST_TIMEOUT
    }, (err, resp) => {
      console.log('[偷听音源] 收到响应')
      
      if (err) {
        console.error('[偷听音源] ❌ 网络错误:', err.message)
        return reject(new Error(`网络请求失败: ${err.message}`))
      }
      
      console.log('[偷听音源] 响应状态码:', resp.statusCode)
      
      if (resp.statusCode !== 200) {
        console.error('[偷听音源] ❌ HTTP错误:', resp.statusCode)
        return reject(new Error(`HTTP错误: ${resp.statusCode}`))
      }
      
      // 解析响应
      const data = safeJsonParse(resp.body)
      if (!data) {
        console.error('[偷听音源] ❌ 响应解析失败')
        return reject(new Error('响应数据解析失败'))
      }
      
      console.log('[偷听音源] 解析后的数据:', data)
      
      // 检查API响应格式
      if (data.status === 200 && data.url) {
        console.log('[偷听音源] ✅ 成功获取URL:', data.url)
        console.log('[偷听音源] 歌曲信息:', `${data.name || '未知'} - ${data.ar_name || '未知'}`)
        console.log('[偷听音源] 实际音质:', data.level)
        
        // 🔥 关键修复：落雪音乐期望直接返回URL字符串，不是对象！
        resolve(data.url)
      } else {
        const errorMsg = data.error || '获取音乐链接失败'
        console.error('[偷听音源] ❌ API错误:', errorMsg)
        reject(new Error(errorMsg))
      }
    })
  })
}

// 注册事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] 📨 收到请求:', { source, action })
  
  // 只处理网易云音乐的musicUrl请求
  if (source === 'wy' && action === 'musicUrl') {
    console.log('[偷听音源] 🎵 处理网易云音乐URL请求')
    return getMusicUrl(info.musicInfo, info.type)
  }
  
  // 不支持的操作
  const errorMsg = `不支持的操作: ${source}/${action}`
  console.error('[偷听音源] ❌', errorMsg)
  return Promise.reject(new Error(errorMsg))
})

// 初始化
console.log('[偷听音源] 🔧 开始初始化...')

send(EVENT_NAMES.inited, {
  openDevTools: false,  // 生产环境关闭调试
  sources: {
    wy: {
      name: '偷听音乐源(修复版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[偷听音源] ✅ 初始化完成')
console.log('[偷听音源] 📊 音质映射:')
console.log('[偷听音源]   128k -> standard (标准音质)')
console.log('[偷听音源]   320k -> lossless (无损音质) ⬆️')
console.log('[偷听音源]   flac -> hires (Hi-Res音质) ⬆️⬆️')
console.log('[偷听音源]   flac24bit -> jymaster (超清母带) ⭐')
console.log('[偷听音源] 🔧 API地址:', CONFIG.API_BASE_URL)
console.log('[偷听音源] 🎵 准备就绪，等待音乐请求...')
