# 🎵 偷听音乐源 - 落雪音乐自定义源

## 📖 简介

这是一个为落雪音乐(LX Music)定制的自定义音源脚本，基于您部署的网易云音乐API服务 `https://suwyy.deno.dev`。

## ✨ 特性

- 🎯 **高品质音源**：支持128k、320k、无损、Hi-Res多种音质
- 🚀 **稳定可靠**：基于您的Deno部署，响应速度快
- 🔧 **智能容错**：完善的错误处理和重试机制
- 📱 **全平台支持**：兼容落雪音乐桌面版和移动版

## 🎼 支持音质

| 落雪音乐音质 | API音质参数 | 说明 |
|-------------|------------|------|
| 128k | standard | 标准音质 |
| 320k | exhigh | 极高音质 |
| flac | lossless | 无损音质 |
| flac24bit | jymaster | **超清母带** (您API的最高音质) |

> **注意**：您的API实际支持7种音质等级 (standard/exhigh/lossless/hires/sky/jyeffect/jymaster)，但落雪音乐自定义源只支持4种。我们将最高的 `jymaster`(超清母带) 映射到 `flac24bit`，让您在落雪音乐中也能享受到最高品质的音乐！

## 📥 安装方法

### 方法一：本地文件导入
1. 下载 `lx-music-custom-source.js` 文件
2. 打开落雪音乐 → 设置 → 自定义源
3. 点击"导入源" → 选择下载的js文件
4. 启用"偷听音乐源"

### 方法二：在线导入
1. 打开落雪音乐 → 设置 → 自定义源
2. 点击"在线导入"
3. 输入脚本URL（如果您将脚本托管在网上）
4. 点击导入并启用

## 🔧 使用步骤

1. **启用音源**
   - 在自定义源列表中找到"偷听音乐源"
   - 确保开关处于开启状态

2. **搜索音乐**
   - 在落雪音乐中正常搜索歌曲
   - 选择来源为"网易云音乐"

3. **选择音质**
   - 在播放或下载时选择所需音质
   - 建议优先选择320k或flac获得最佳体验

4. **播放/下载**
   - 点击播放按钮试听
   - 点击下载按钮保存到本地

## ⚙️ 配置说明

### 脚本配置
```javascript
// API基础地址（您的Deno部署地址）
const API_BASE = 'https://suwyy.deno.dev'

// 请求超时时间（毫秒）
const API_TIMEOUT = 15000

// 音质映射关系
const qualityMap = {
  '128k': 'standard',
  '320k': 'higher', 
  'flac': 'lossless',
  'flac24bit': 'hires'
}
```

### 调试模式
如需调试，可将脚本中的 `openDevTools` 设置为 `true`：
```javascript
send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启调试模式
  sources: { ... }
})
```

## 🐛 故障排除

### 常见问题

1. **无法获取音乐链接**
   - 检查网络连接是否正常
   - 确认API服务 `https://suwyy.deno.dev` 可访问
   - 查看控制台错误信息

2. **音质选择无效**
   - 确认选择的音质在支持列表中
   - 某些歌曲可能不支持高音质，会自动降级

3. **脚本初始化失败**
   - 检查脚本文件格式是否为UTF-8
   - 确认JavaScript语法无误
   - 重新导入脚本文件

### 错误代码说明

| 错误信息 | 原因 | 解决方法 |
|---------|------|---------|
| 网络连接失败 | 无法访问API服务 | 检查网络和API状态 |
| 歌曲信息无效 | 歌曲ID格式错误 | 重新搜索歌曲 |
| 音源服务暂时不可用 | API服务异常 | 稍后重试 |

## 📊 性能优化

- **请求超时**：设置为15秒，平衡速度和稳定性
- **错误重试**：自动处理网络波动
- **日志记录**：详细的调试信息便于问题定位

## 🔒 隐私说明

- 本脚本仅调用您自己部署的API服务
- 不收集任何用户个人信息
- 所有请求都通过HTTPS加密传输

## 📝 更新日志

### v1.0.0 (2025-01-27)
- 🎉 首次发布
- ✅ 支持4种音质等级
- ✅ 完善的错误处理机制
- ✅ 兼容落雪音乐最新版本

## 🤝 技术支持

如遇到问题，请提供以下信息：
1. 落雪音乐版本号
2. 操作系统版本
3. 错误截图或日志
4. 具体操作步骤

## ⚖️ 免责声明

本脚本仅供学习交流使用，请遵守相关法律法规和服务条款。使用本脚本所产生的任何问题由用户自行承担。

---

**享受高品质音乐体验！** 🎵
