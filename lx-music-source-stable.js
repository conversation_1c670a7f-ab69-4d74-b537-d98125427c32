/**
 * @name 偷听音乐源
 * @description 网易云音乐解析 - 稳定版
 * @version 1.0.1
 * <AUTHOR>
 * @homepage https://suwyy.deno.dev
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

// 增强的HTTP请求，包含重试机制
const httpRequest = (url, options, retries = 2) => new Promise((resolve, reject) => {
  const makeRequest = (attempt) => {
    console.log(`[偷听音源] 发送请求 (尝试 ${attempt + 1}/${retries + 1}):`, url)
    
    request(url, {
      ...options,
      timeout: 10000, // 减少超时时间
    }, (err, resp) => {
      if (err) {
        console.error(`[偷听音源] 请求失败 (尝试 ${attempt + 1}):`, err.message)
        
        if (attempt < retries && (err.message.includes('timeout') || err.message.includes('hang up'))) {
          console.log(`[偷听音源] 准备重试...`)
          setTimeout(() => makeRequest(attempt + 1), 1000 * (attempt + 1))
        } else {
          reject(err)
        }
      } else {
        console.log(`[偷听音源] 请求成功 (尝试 ${attempt + 1})`)
        resolve(resp.body)
      }
    })
  }
  
  makeRequest(0)
})

const apis = {
  wy: {
    musicUrl({ songmid, id, songId, rid }, quality) {
      console.log('[偷听音源] ========== 开始处理 ==========')
      console.log('[偷听音源] 接收参数:', { songmid, id, songId, rid, quality })
      
      // 提取歌曲ID
      const songIdValue = songmid || id || songId || rid
      if (!songIdValue) {
        console.error('[偷听音源] 无法提取歌曲ID')
        return Promise.reject(new Error('无法提取歌曲ID'))
      }
      
      console.log('[偷听音源] 使用歌曲ID:', songIdValue)
      console.log('[偷听音源] 使用音质:', quality)
      
      // 构建请求数据
      const requestData = {
        ids: String(songIdValue),
        level: quality || 'lossless'
      }
      
      console.log('[偷听音源] 请求数据:', requestData)
      
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        body: JSON.stringify(requestData)
      }).then(responseBody => {
        console.log('[偷听音源] 原始响应:', responseBody)
        
        try {
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          console.log('[偷听音源] 解析后数据:', data)
          
          if (data && data.status === 200 && data.url) {
            console.log('[偷听音源] ✅ 成功获取URL:', data.url)
            console.log('[偷听音源] 歌曲信息:', data.name || '未知')
            
            // 确保URL有效
            const finalUrl = data.url
            if (!finalUrl || !finalUrl.startsWith('http')) {
              throw new Error('无效的URL: ' + finalUrl)
            }
            
            return finalUrl
          } else {
            const error = (data && data.error) || '获取音乐链接失败'
            console.error('[偷听音源] API错误:', error)
            throw new Error(error)
          }
        } catch (parseError) {
          console.error('[偷听音源] 解析错误:', parseError.message)
          throw new Error('响应解析失败: ' + parseError.message)
        }
      }).catch(error => {
        console.error('[偷听音源] 最终错误:', error.message)
        
        // 根据错误类型提供更友好的错误信息
        if (error.message.includes('timeout')) {
          throw new Error('请求超时，请检查网络连接')
        } else if (error.message.includes('hang up')) {
          throw new Error('网络连接中断，请稍后重试')
        } else {
          throw error
        }
      })
    },
  }
}

// 事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] ========== 收到请求 ==========')
  console.log('[偷听音源] source:', source)
  console.log('[偷听音源] action:', action)
  console.log('[偷听音源] info:', info)
  
  switch (action) {
    case 'musicUrl':
      if (!apis[source]) {
        console.error('[偷听音源] 不支持的源:', source)
        return Promise.reject(new Error('不支持的源: ' + source))
      }
      
      if (!qualitys[source] || !qualitys[source][info.type]) {
        console.error('[偷听音源] 不支持的音质:', info.type)
        return Promise.reject(new Error('不支持的音质: ' + info.type))
      }
      
      const quality = qualitys[source][info.type]
      console.log('[偷听音源] 音质映射:', info.type, '->', quality)
      
      return apis[source].musicUrl(info.musicInfo, quality).catch(err => {
        console.error('[偷听音源] 处理失败:', err.message)
        return Promise.reject(err)
      })
    
    default:
      console.error('[偷听音源] 不支持的操作:', action)
      return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

// 初始化
console.log('[偷听音源] 开始初始化...')

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源(稳定版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[偷听音源] ========== 初始化完成 ==========')
console.log('[偷听音源] 版本: 稳定版 v1.0.1')
console.log('[偷听音源] 支持源: 网易云音乐 (wy)')
console.log('[偷听音源] 支持音质: 128k, 320k, flac, flac24bit')
console.log('[偷听音源] 特性: 自动重试、错误恢复、详细日志')
console.log('[偷听音源] 🎵 准备就绪')
