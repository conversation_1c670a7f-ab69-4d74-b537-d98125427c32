/**
 * @name 测试音乐源
 * @description 最简单的测试版本
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[测试] 脚本开始加载')
console.log('[测试] 时间:', new Date().toLocaleString())

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  console.log('[测试] 发送HTTP请求:', url)
  request(url, options, (err, resp) => {
    console.log('[测试] HTTP响应 - 错误:', err)
    console.log('[测试] HTTP响应 - 状态:', resp?.statusCode)
    if (err) return reject(err)
    resolve(resp.body)
  })
})

const apis = {
  wy: {
    musicUrl({ hash, songmid }, quality) {
      console.log('[测试] musicUrl被调用')
      console.log('[测试] 参数:', { hash, songmid, quality })
      
      const songId = hash ?? songmid ?? '1901371647'
      console.log('[测试] 使用歌曲ID:', songId)
      
      // 先尝试一个简单的测试URL
      console.log('[测试] 返回测试URL')
      return Promise.resolve('https://music.163.com/song/media/outer/url?id=1901371647.mp3')
      
      // 如果测试URL能工作，再启用真实API
      /*
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: songId,
          level: quality
        })
      }).then(data => {
        const result = typeof data === 'string' ? JSON.parse(data) : data
        if (result && result.status === 200 && result.url) {
          return result.url
        }
        throw new Error('获取失败')
      })
      */
    },
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[测试] 收到请求:', { source, action })
  console.log('[测试] info:', info)
  
  switch (action) {
    case 'musicUrl':
      console.log('[测试] 处理musicUrl请求')
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type]).catch(err => {
        console.log('[测试] 错误:', err)
        return Promise.reject(err)
      })
    default:
      console.log('[测试] 不支持的操作:', action)
      return Promise.reject(new Error('不支持的操作'))
  }
})

send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启调试工具
  sources: {
    wy: {
      name: '测试音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[测试] 脚本初始化完成')

// 定时输出，确认脚本运行
setInterval(() => {
  console.log('[测试] 脚本正在运行...', new Date().toLocaleTimeString())
}, 10000)
