/**
 * @name 偷听音乐源(优化版)
 * @description 优化音质映射，分析PC/移动端差异
 * @version 1.1.0
 * <AUTHOR> 4.0 sonnet
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

// 优化的音质映射
const qualityMap = {
  '128k': 'standard',      // 标准音质
  '320k': 'lossless',      // 320k映射到无损音质 (大幅提升)
  'flac': 'hires',         // 无损映射到Hi-Res音质 (进一步提升)
  'flac24bit': 'jymaster'  // 超清母带 (最高音质)
}

// 检测运行环境
function detectEnvironment() {
  const userAgent = navigator.userAgent || ''
  const isPC = !(/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent))
  
  console.log('[偷听音源] 运行环境检测:')
  console.log('[偷听音源] User Agent:', userAgent)
  console.log('[偷听音源] 是否PC端:', isPC)
  
  return {
    isPC,
    isMobile: !isPC,
    platform: isPC ? 'desktop' : 'mobile'
  }
}

// 获取音乐链接
const getMusicUrl = (musicInfo, quality) => {
  return new Promise((resolve, reject) => {
    const env = detectEnvironment()
    console.log(`[偷听音源] 开始获取音乐链接 - 平台: ${env.platform}`)
    console.log('[偷听音源] musicInfo:', musicInfo)
    console.log('[偷听音源] quality:', quality)
    
    // 提取歌曲ID
    const songId = musicInfo.songmid || musicInfo.id || musicInfo.songId || musicInfo.rid
    console.log('[偷听音源] 提取的歌曲ID:', songId)
    
    if (!songId) {
      console.log('[偷听音源] 错误: 无法提取歌曲ID')
      return reject(new Error('无法提取歌曲ID'))
    }

    const apiQuality = qualityMap[quality] || 'lossless'
    console.log('[偷听音源] 音质映射:', quality, '->', apiQuality)

    const requestData = {
      ids: String(songId),
      level: apiQuality
    }
    
    console.log('[偷听音源] 请求数据:', requestData)

    // 根据平台调整请求头
    const headers = {
      'Content-Type': 'application/json',
      'User-Agent': env.isPC ? 
        'LX-Music-Desktop/2.0.0' : 
        'LX-Music-Mobile/1.0.0'
    }
    
    console.log('[偷听音源] 请求头:', headers)

    request('https://suwyy.deno.dev/api/song', {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData),
      timeout: 15000
    }, (err, resp) => {
      console.log('[偷听音源] 收到响应')
      
      if (err) {
        console.log('[偷听音源] 网络错误:', err)
        return reject(new Error(`网络请求失败: ${err.message}`))
      }

      console.log('[偷听音源] 响应状态:', resp.statusCode)
      console.log('[偷听音源] 响应体类型:', typeof resp.body)

      try {
        // 安全处理响应体
        let data
        if (typeof resp.body === 'string') {
          console.log('[偷听音源] 解析JSON字符串')
          data = JSON.parse(resp.body)
        } else {
          console.log('[偷听音源] 直接使用对象')
          data = resp.body
        }
        
        console.log('[偷听音源] 最终数据:', data)
        
        if (resp.statusCode !== 200) {
          console.log('[偷听音源] HTTP错误:', resp.statusCode)
          return reject(new Error(`HTTP ${resp.statusCode}: ${data.error || '请求失败'}`))
        }
        
        if (data.status === 200 && data.url) {
          console.log('[偷听音源] 成功获取URL:', data.url)
          console.log('[偷听音源] 歌曲信息:', `${data.name} - ${data.ar_name}`)
          console.log('[偷听音源] 实际音质:', data.level)
          resolve(data.url)
        } else {
          console.log('[偷听音源] API错误:', data.error)
          reject(new Error(data.error || '获取音乐链接失败'))
        }
      } catch (parseError) {
        console.log('[偷听音源] 处理错误:', parseError)
        console.log('[偷听音源] 原始响应:', resp.body)
        reject(new Error(`响应处理失败: ${parseError.message}`))
      }
    })
  })
}

// 注册事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] 收到落雪音乐请求:', { source, action, info })
  
  if (source === 'wy' && action === 'musicUrl') {
    return getMusicUrl(info.musicInfo, info.type)
  }
  
  return Promise.reject(new Error(`不支持的操作: ${source}/${action}`))
})

// 初始化
const env = detectEnvironment()
console.log(`[偷听音源] 开始初始化 - 平台: ${env.platform}`)

send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启调试工具
  sources: {
    wy: {
      name: `偷听音乐源(${env.platform})`,
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[偷听音源] 初始化完成')
console.log('[偷听音源] 优化音质映射:')
console.log('[偷听音源] 128k -> standard (标准)')
console.log('[偷听音源] 320k -> lossless (无损) ⬆️')
console.log('[偷听音源] flac -> hires (Hi-Res) ⬆️⬆️')
console.log('[偷听音源] flac24bit -> jymaster (超清母带) ⭐')
