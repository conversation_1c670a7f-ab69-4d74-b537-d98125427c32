/**
 * @name 偷听音乐源(官方规范版)
 * @description 基于落雪音乐官方文档规范重构，支持多平台优化
 * @version 2.0.0
 * <AUTHOR> 4.0 sonnet
 * @homepage https://github.com/your-repo/lx-music-source
 */

const { EVENT_NAMES, request, on, send, utils, env: lxEnv, version: lxVersion } = globalThis.lx

// 脚本信息日志
console.log(`[偷听音源] LX Music API版本: ${lxVersion}`)
console.log(`[偷听音源] 运行环境: ${lxEnv}`)
console.log(`[偷听音源] 脚本版本: 2.0.0`)

// 配置常量
const CONFIG = {
  // API配置 - 修正为您的实际API地址
  API_BASE_URL: 'http://localhost:3004/api',  // 本地测试
  // API_BASE_URL: 'https://suwyy.deno.dev/api',  // 线上地址
  REQUEST_TIMEOUT: 15000,

  // 音质映射 (根据用户偏好优化)
  QUALITY_MAP: {
    '128k': 'standard',      // 标准音质
    '320k': 'lossless',      // 320k映射到无损音质 (大幅提升)
    'flac': 'hires',         // 无损映射到Hi-Res音质 (进一步提升)
    'flac24bit': 'jymaster'  // 超清母带 (最高音质)
  },

  // 支持的音质列表
  SUPPORTED_QUALITIES: ['128k', '320k', 'flac', 'flac24bit'],

  // 重试配置
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000
}

// 工具函数
const utils_custom = {
  // 环境检测 (基于官方API)
  getEnvironmentInfo() {
    const isDesktop = lxEnv === 'desktop'

    console.log('[偷听音源] 环境信息:')
    console.log('[偷听音源] LX环境:', lxEnv)
    console.log('[偷听音源] 是否桌面版:', isDesktop)

    return {
      isDesktop,
      isMobile: !isDesktop,
      platform: lxEnv,
      apiVersion: lxVersion
    }
  },

  // 延迟函数
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  },

  // 安全的JSON解析
  safeJsonParse(str, defaultValue = null) {
    try {
      return typeof str === 'string' ? JSON.parse(str) : str
    } catch (error) {
      console.warn('[偷听音源] JSON解析失败:', error.message)
      return defaultValue
    }
  },

  // 提取歌曲ID的多种方式
  extractSongId(musicInfo) {
    const possibleIds = [
      musicInfo.songmid,
      musicInfo.id,
      musicInfo.songId,
      musicInfo.rid,
      musicInfo.hash,
      musicInfo.songid
    ]

    for (const id of possibleIds) {
      if (id && String(id).trim()) {
        return String(id).trim()
      }
    }

    return null
  },

  // 重试机制
  async retry(fn, maxRetries = CONFIG.MAX_RETRIES, delay = CONFIG.RETRY_DELAY) {
    let lastError

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error
        console.warn(`[偷听音源] 重试 ${i + 1}/${maxRetries + 1} 失败:`, error.message)

        if (i < maxRetries) {
          await this.delay(delay * (i + 1)) // 递增延迟
        }
      }
    }

    throw lastError
  }
}

// 获取音乐链接 (基于官方规范重构)
const getMusicUrl = (musicInfo, quality) => {
  return new Promise((resolve, reject) => {
    const envInfo = utils_custom.getEnvironmentInfo()
    console.log(`[偷听音源] 开始获取音乐链接 - 平台: ${envInfo.platform}`)
    console.log('[偷听音源] musicInfo:', musicInfo)
    console.log('[偷听音源] quality:', quality)
    
    // 提取歌曲ID (支持多种格式)
    const songId = utils_custom.extractSongId(musicInfo)
    console.log('[偷听音源] 提取的歌曲ID:', songId)

    if (!songId) {
      console.log('[偷听音源] 错误: 无法提取歌曲ID')
      console.log('[偷听音源] 支持的ID字段: songmid, id, songId, rid, hash, songid')
      return reject(new Error('无法提取歌曲ID，请检查歌曲信息格式'))
    }

    const apiQuality = CONFIG.QUALITY_MAP[quality] || 'lossless'
    console.log('[偷听音源] 音质映射:', quality, '->', apiQuality)

    const requestData = {
      ids: String(songId),
      level: apiQuality
    }
    
    console.log('[偷听音源] 请求数据:', requestData)

    // 根据平台调整请求头 (基于官方规范)
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'User-Agent': envInfo.isDesktop ?
        `LX-Music-Desktop/${lxVersion}` :
        `LX-Music-Mobile/${lxVersion}`,
      'X-LX-Platform': envInfo.platform,
      'Cache-Control': 'no-cache'
    }
    
    console.log('[偷听音源] 请求头:', headers)

    // 发送API请求 (使用配置的超时时间)
    request(`${CONFIG.API_BASE_URL}/song`, {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData),
      timeout: CONFIG.REQUEST_TIMEOUT
    }, (err, resp) => {
      console.log('[偷听音源] 收到响应')
      
      if (err) {
        console.log('[偷听音源] 网络错误:', err)
        return reject(new Error(`网络请求失败: ${err.message}`))
      }

      console.log('[偷听音源] 响应状态:', resp.statusCode)
      console.log('[偷听音源] 响应体类型:', typeof resp.body)

      try {
        // 安全处理响应体 (使用工具函数)
        const data = utils_custom.safeJsonParse(resp.body)
        if (!data) {
          throw new Error('响应数据解析失败')
        }
        
        console.log('[偷听音源] 最终数据:', data)
        
        if (resp.statusCode !== 200) {
          console.log('[偷听音源] HTTP错误:', resp.statusCode)
          return reject(new Error(`HTTP ${resp.statusCode}: ${data.error || '请求失败'}`))
        }
        
        if (data.status === 200 && data.url) {
          console.log('[偷听音源] ✅ 成功获取URL:', data.url)
          console.log('[偷听音源] 歌曲信息:', `${data.name || '未知'} - ${data.ar_name || '未知'}`)
          console.log('[偷听音源] 实际音质:', data.level)
          console.log('[偷听音源] 文件大小:', data.size || '未知')

          // ⚠️ 重要：落雪音乐期望直接返回URL字符串，不是对象！
          resolve(data.url)
        } else {
          console.log('[偷听音源] ❌ API错误:', data.error)
          reject(new Error(data.error || '获取音乐链接失败'))
        }
      } catch (parseError) {
        console.log('[偷听音源] 处理错误:', parseError)
        console.log('[偷听音源] 原始响应:', resp.body)
        reject(new Error(`响应处理失败: ${parseError.message}`))
      }
    })
  })
}

// 注册事件处理 (基于官方规范)
on(EVENT_NAMES.request, async ({ source, action, info }) => {
  console.log('[偷听音源] 📨 收到落雪音乐请求:', { source, action })
  console.log('[偷听音源] 请求信息:', info)

  try {
    // 处理音乐URL获取请求
    if (source === 'wy' && action === 'musicUrl') {
      const result = await getMusicUrl(info.musicInfo, info.type)
      console.log('[偷听音源] 🎵 请求处理成功')
      return result
    }

    // 未支持的操作
    const errorMsg = `不支持的操作: ${source}/${action}`
    console.log('[偷听音源] ❌', errorMsg)
    throw new Error(errorMsg)

  } catch (error) {
    console.error('[偷听音源] 💥 请求处理失败:', error.message)
    throw error
  }
})

// 初始化脚本 (基于官方规范)
const envInfo = utils_custom.getEnvironmentInfo()
console.log(`[偷听音源] 🚀 开始初始化 - 平台: ${envInfo.platform}`)

// 发送初始化完成事件
send(EVENT_NAMES.inited, {
  openDevTools: false, // 生产环境关闭调试工具
  sources: {
    wy: {
      name: `偷听音乐源(${envInfo.platform}版)`,
      type: 'music',
      actions: ['musicUrl'],
      qualitys: CONFIG.SUPPORTED_QUALITIES,
      supportedQualitys: CONFIG.SUPPORTED_QUALITIES
    }
  }
})

console.log('[偷听音源] ✅ 初始化完成')
console.log('[偷听音源] 📊 音质映射配置:')
console.log('[偷听音源]   128k -> standard (标准音质)')
console.log('[偷听音源]   320k -> lossless (无损音质) ⬆️')
console.log('[偷听音源]   flac -> hires (Hi-Res音质) ⬆️⬆️')
console.log('[偷听音源]   flac24bit -> jymaster (超清母带) ⭐')
console.log('[偷听音源] 🔧 配置信息:')
console.log(`[偷听音源]   API地址: ${CONFIG.API_BASE_URL}`)
console.log(`[偷听音源]   请求超时: ${CONFIG.REQUEST_TIMEOUT}ms`)
console.log(`[偷听音源]   最大重试: ${CONFIG.MAX_RETRIES}次`)
console.log(`[偷听音源]   LX版本: ${lxVersion}`)
console.log('[偷听音源] 🎵 准备就绪，等待音乐请求...')
