# 🎵 音质映射详细说明

## 📊 您的API支持的完整音质等级

根据您的 `netease-eapi.ts` 代码分析，您的API支持以下7种音质等级：

| API参数 | 中文名称 | 比特率范围 | 说明 |
|---------|---------|-----------|------|
| `standard` | 标准音质 | ~128kbps | 基础音质，适合流量有限场景 |
| `exhigh` | 极高音质 | ~320kbps | 高品质MP3，平衡音质与文件大小 |
| `lossless` | 无损音质 | ~900kbps+ | FLAC无损格式，CD音质 |
| `hires` | Hi-Res音质 | ~1000kbps+ | 高解析度音频 |
| `sky` | 沉浸环绕声 | 变动 | 空间音频技术 |
| `jyeffect` | 高清环绕声 | 变动 | 增强环绕声效果 |
| `jymaster` | **超清母带** | ~2000kbps+ | **最高音质，母带级别** |

## 🔄 落雪音乐限制与映射策略

### 落雪音乐支持的音质
落雪音乐自定义源API只支持4种固定音质：
- `128k` - 对应128kbps
- `320k` - 对应320kbps  
- `flac` - 对应无损音质
- `flac24bit` - 对应24bit无损

### 🎯 我们的映射策略

```javascript
const qualityMap = {
  '128k': 'standard',      // 标准音质 → 128k
  '320k': 'exhigh',        // 极高音质 → 320k  
  'flac': 'lossless',      // 无损音质 → flac
  'flac24bit': 'jymaster'  // 超清母带 → flac24bit ⭐
}
```

### 🌟 关键优化点

**`flac24bit` → `jymaster` 映射**
- 这是最重要的优化！
- 将落雪音乐的最高音质选项映射到您API的最高音质
- 用户选择 `flac24bit` 时，实际获得的是 `jymaster`(超清母带)
- 音质比标准的24bit FLAC更高

## 📈 音质对比

| 选择 | 实际获得 | 预期比特率 | 音质等级 |
|------|---------|-----------|---------|
| 128k | standard | ~128kbps | ⭐ |
| 320k | exhigh | ~320kbps | ⭐⭐ |
| flac | lossless | ~900kbps | ⭐⭐⭐ |
| flac24bit | **jymaster** | **~2000kbps+** | ⭐⭐⭐⭐⭐ |

## 🚀 未使用的高级音质

由于落雪音乐的限制，以下音质暂时无法直接使用：
- `hires` - Hi-Res音质
- `sky` - 沉浸环绕声  
- `jyeffect` - 高清环绕声

但通过我们的映射策略，用户仍然可以通过选择 `flac24bit` 获得最高的 `jymaster` 音质。

## 💡 使用建议

### 对于普通用户
- **日常听歌**：选择 `320k` (极高音质)
- **高品质需求**：选择 `flac` (无损音质)
- **发烧友/专业用途**：选择 `flac24bit` (超清母带)

### 对于音质发烧友
- 强烈推荐使用 `flac24bit` 选项
- 这将为您提供 `jymaster` 超清母带音质
- 比特率可达2000kbps以上，接近原始录音室母带品质

## 🔧 技术实现

脚本会自动处理音质映射：

```javascript
// 用户在落雪音乐中选择 flac24bit
const apiQuality = qualityMap['flac24bit']; // 得到 'jymaster'

// 发送到您的API
const requestData = {
  ids: songId,
  level: 'jymaster'  // 实际请求超清母带
};
```

## 📝 总结

通过巧妙的音质映射，我们成功将您API的7种音质压缩到落雪音乐支持的4种选项中，**特别是将最高的母带音质映射到 `flac24bit`**，确保用户能够获得最佳的音乐体验！

---

**享受超清母带音质！** 🎵✨
