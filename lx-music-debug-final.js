/**
 * @name 偷听音乐源(终极调试版)
 * @description 找出无法获取歌曲链接的根本原因
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[调试] ========== 脚本开始加载 ==========')
console.log('[调试] 时间:', new Date().toLocaleString())
console.log('[调试] globalThis.lx 存在:', !!globalThis.lx)
console.log('[调试] EVENT_NAMES:', EVENT_NAMES)

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

console.log('[调试] 音质映射配置:', qualitys)

// 调试版本的请求函数
const h = (u, o = { method: 'GET' }) => {
  console.log('[调试] ========== HTTP请求开始 ==========')
  console.log('[调试] URL:', u)
  console.log('[调试] 选项:', JSON.stringify(o, null, 2))
  
  return new Promise((resolve, reject) => {
    request(u, o, (err, resp) => {
      console.log('[调试] ========== HTTP响应 ==========')
      console.log('[调试] 错误:', err)
      console.log('[调试] 响应对象存在:', !!resp)
      
      if (resp) {
        console.log('[调试] 状态码:', resp.statusCode)
        console.log('[调试] 响应头:', resp.headers)
        console.log('[调试] 响应体类型:', typeof resp.body)
        console.log('[调试] 响应体长度:', resp.body?.length)
        console.log('[调试] 响应体内容:', resp.body)
      }
      
      if (err) {
        console.error('[调试] ❌ 请求失败:', err)
        return reject(err)
      }
      
      console.log('[调试] ✅ 请求成功，返回响应体')
      resolve(resp.body)
    })
  })
}

const apis = {
  wy: {
    musicUrl({ hash, songmid, id, songId }, quality) {
      console.log('[调试] ========== musicUrl 函数被调用 ==========')
      console.log('[调试] 传入参数:')
      console.log('[调试]   hash:', hash)
      console.log('[调试]   songmid:', songmid)
      console.log('[调试]   id:', id)
      console.log('[调试]   songId:', songId)
      console.log('[调试]   quality:', quality)
      
      // 提取歌曲ID
      const extractedId = hash ?? songmid ?? id ?? songId
      console.log('[调试] 提取到的歌曲ID:', extractedId)
      
      if (!extractedId) {
        console.error('[调试] ❌ 无法提取歌曲ID')
        return Promise.reject(new Error('无法提取歌曲ID'))
      }
      
      // 构建请求数据
      const requestData = {
        ids: String(extractedId),
        level: quality
      }
      
      console.log('[调试] 构建的请求数据:', requestData)
      console.log('[调试] 准备发送POST请求到API...')
      
      return h('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        body: JSON.stringify(requestData),
        timeout: 15000
      }).then(responseBody => {
        console.log('[调试] ========== 处理API响应 ==========')
        console.log('[调试] 原始响应体:', responseBody)
        console.log('[调试] 响应体类型:', typeof responseBody)
        
        if (!responseBody) {
          console.error('[调试] ❌ 响应体为空')
          throw new Error('API返回空响应')
        }
        
        let parsedData
        try {
          parsedData = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          console.log('[调试] ✅ JSON解析成功')
          console.log('[调试] 解析后的数据:', JSON.stringify(parsedData, null, 2))
        } catch (parseError) {
          console.error('[调试] ❌ JSON解析失败:', parseError.message)
          console.error('[调试] 原始响应体:', responseBody)
          throw new Error('JSON解析失败: ' + parseError.message)
        }
        
        // 检查响应结构
        console.log('[调试] ========== 检查响应结构 ==========')
        console.log('[调试] parsedData.status:', parsedData.status)
        console.log('[调试] parsedData.url:', parsedData.url)
        console.log('[调试] parsedData.error:', parsedData.error)
        
        if (parsedData && parsedData.status === 200 && parsedData.url) {
          console.log('[调试] ✅ API调用成功!')
          console.log('[调试] 歌曲名称:', parsedData.name)
          console.log('[调试] 歌手名称:', parsedData.ar_name)
          console.log('[调试] 音质等级:', parsedData.level)
          console.log('[调试] 文件大小:', parsedData.size)
          console.log('[调试] 比特率:', parsedData.br)
          console.log('[调试] 最终URL:', parsedData.url)
          console.log('[调试] ========== 返回URL给落雪音乐 ==========')
          
          return parsedData.url
        } else {
          const errorMsg = parsedData?.error || `API返回错误状态: ${parsedData?.status}`
          console.error('[调试] ❌ API调用失败:', errorMsg)
          console.error('[调试] 完整响应数据:', parsedData)
          throw new Error(errorMsg)
        }
      }).catch(error => {
        console.error('[调试] ========== 最终错误处理 ==========')
        console.error('[调试] 错误类型:', error.constructor.name)
        console.error('[调试] 错误消息:', error.message)
        console.error('[调试] 错误堆栈:', error.stack)
        throw error
      })
    }
  }
}

console.log('[调试] API对象创建完成:', Object.keys(apis))

// 事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[调试] ========== 收到落雪音乐请求 ==========')
  console.log('[调试] source:', source)
  console.log('[调试] action:', action)
  console.log('[调试] info 完整对象:', JSON.stringify(info, null, 2))
  
  if (action === 'musicUrl') {
    console.log('[调试] 处理 musicUrl 请求')
    
    // 检查源支持
    if (!apis[source]) {
      console.error('[调试] ❌ 不支持的源:', source)
      console.error('[调试] 可用源:', Object.keys(apis))
      return Promise.reject(new Error('不支持的源: ' + source))
    }
    
    // 检查音质支持
    if (!qualitys[source] || !qualitys[source][info.type]) {
      console.error('[调试] ❌ 不支持的音质:', info.type)
      console.error('[调试] 可用音质:', Object.keys(qualitys[source] || {}))
      return Promise.reject(new Error('不支持的音质: ' + info.type))
    }
    
    const mappedQuality = qualitys[source][info.type]
    console.log('[调试] ✅ 音质映射成功:', info.type, '->', mappedQuality)
    
    console.log('[调试] 准备调用 apis[' + source + '].musicUrl...')
    return apis[source].musicUrl(info.musicInfo, mappedQuality).catch(err => {
      console.error('[调试] ❌ musicUrl 执行失败:', err.message)
      return Promise.reject(err)
    })
  } else {
    console.error('[调试] ❌ 不支持的操作:', action)
    return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

console.log('[调试] 事件处理器注册完成')

// 初始化
console.log('[调试] 准备发送初始化事件...')

send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启调试工具
  sources: {
    wy: {
      name: '偷听音乐源(终极调试版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[调试] ========== 初始化完成 ==========')
console.log('[调试] 脚本加载完毕，等待落雪音乐调用...')

// 定时心跳，确认脚本持续运行
let heartbeat = 0
setInterval(() => {
  heartbeat++
  console.log(`[调试] 心跳 ${heartbeat} - 脚本正在运行...`)
}, 30000) // 每30秒输出一次
