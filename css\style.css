@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* 基础重置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
    -webkit-touch-callout: none; /* 禁用长按菜单 */
}

/* 移动端触摸优化 */
button, input, select, textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    touch-action: manipulation; /* 优化触摸响应 */
}

/* 防止移动端双击缩放 */
button {
    touch-action: manipulation;
}

/* 主体样式 */
body {
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Inter', 'Georgia', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'PingFang SC', 'Microsoft YaHei', sans-serif;
    background:
        radial-gradient(circle at 15% 85%, rgba(120, 119, 198, 0.2) 0%, transparent 60%),
        radial-gradient(circle at 85% 15%, rgba(255, 107, 157, 0.25) 0%, transparent 55%),
        radial-gradient(circle at 45% 45%, rgba(196, 113, 237, 0.15) 0%, transparent 65%),
        radial-gradient(circle at 70% 80%, rgba(18, 194, 233, 0.1) 0%, transparent 50%),
        linear-gradient(135deg,
            #0a0a0f 0%,
            #1a1a2e 20%,
            #16213e 40%,
            #0f3460 60%,
            #1a1a2e 80%,
            #0a0a0f 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    animation: lonelyBackgroundFlow 30s ease-in-out infinite;
}

/* 背景动画 */
@keyframes lonelyBackgroundFlow {
    0%, 100% {
        background-position: 0% 0%, 100% 100%, 50% 50%, 30% 70%, 0% 0%;
        background-size: 120% 120%, 110% 110%, 130% 130%, 100% 100%, 100% 100%;
    }
    25% {
        background-position: 20% 30%, 80% 70%, 60% 40%, 50% 50%, 5% 5%;
        background-size: 130% 130%, 120% 120%, 140% 140%, 110% 110%, 105% 105%;
    }
    50% {
        background-position: 40% 60%, 60% 40%, 70% 30%, 70% 30%, 10% 10%;
        background-size: 140% 140%, 130% 130%, 150% 150%, 120% 120%, 110% 110%;
    }
    75% {
        background-position: 70% 20%, 30% 80%, 80% 20%, 20% 80%, 15% 15%;
        background-size: 130% 130%, 120% 120%, 140% 140%, 110% 110%, 105% 105%;
    }
}

/* 孤独美学装饰元素 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.02) 0%, transparent 30%),
        radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.01) 0%, transparent 25%);
    pointer-events: none;
    z-index: 1;
    animation: shadowDance 20s ease-in-out infinite;
}

@keyframes shadowDance {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

/* 主容器 */
.main-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 60px 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.container {
    background: rgba(26, 26, 46, 0.85);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 40px;
    padding: 64px;
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.6),
        0 0 160px rgba(255, 107, 157, 0.1),
        inset 0 2px 0 rgba(255, 255, 255, 0.1),
        inset 0 -2px 0 rgba(255, 255, 255, 0.05);
    position: relative;
    overflow: hidden;
    width: 100%;
    max-width: 800px;
}

/* 容器内部光影效果 */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 20%, rgba(255, 107, 157, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(196, 113, 237, 0.03) 0%, transparent 40%);
    pointer-events: none;
    border-radius: 40px;
    animation: innerGlow 12s ease-in-out infinite;
}

@keyframes innerGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.7; }
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 15px;
    margin-top: -30px;
    position: relative;
}

h1 {
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 20px;
    font-size: 3.2em;
    font-weight: 800;
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Georgia', serif;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 107, 157, 0.8) 30%,
        rgba(196, 113, 237, 0.8) 70%,
        rgba(255, 255, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
    animation: titleGlow 6s ease-in-out infinite;
}

@keyframes titleGlow {
    0%, 100% {
        text-shadow: 0 4px 20px rgba(255, 107, 157, 0.3);
        transform: scale(1);
    }
    50% {
        text-shadow: 0 6px 30px rgba(196, 113, 237, 0.5);
        transform: scale(1.02);
    }
}



/* 文学引用样式 */
.literary-quote {
    color: rgba(255, 255, 255, 0.95);
    font-size: 1.35em; /* 调整字体大小，确保文字保持在一行 */
    font-style: italic;
    margin: 40px 0; /* 增加上下边距以占满副标题位置 */
    padding: 32px 24px; /* 增加内边距 */
    background: rgba(22, 33, 62, 0.7);
    border: 2px solid rgba(255, 107, 157, 0.2);
    border-radius: 24px;
    position: relative;
    animation: lonelyFloat 12s ease-in-out infinite;
    text-align: center;
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Georgia', 'Crimson Text', 'Times New Roman', serif;
    line-height: 1.8; /* 调整行距，让文字更易读 */
    box-shadow:
        0 16px 32px rgba(0, 0, 0, 0.4),
        0 0 60px rgba(255, 107, 157, 0.15),
        inset 0 2px 0 rgba(255, 255, 255, 0.1);
    font-weight: 300;
    letter-spacing: 0.5px;
}

/* 文学引用内部元素样式 */
.quote-text {
    margin-bottom: 15px;
}

.quote-author {
    line-height: 0.2; /* 作者行的行距设为0.2 */
    margin-top: 10px;
    font-size: 0.9em;
    opacity: 0.8;
}

@keyframes lonelyFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotateX(0deg);
        box-shadow:
            0 25px 60px rgba(0, 0, 0, 0.4),
            0 0 100px rgba(255, 107, 157, 0.15),
            inset 0 2px 0 rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 107, 157, 0.2);
    }
    33% {
        transform: translateY(-15px) scale(1.02) rotateX(1deg);
        box-shadow:
            0 35px 80px rgba(0, 0, 0, 0.5),
            0 0 120px rgba(196, 113, 237, 0.25),
            inset 0 2px 0 rgba(255, 255, 255, 0.2);
        border-color: rgba(196, 113, 237, 0.3);
    }
    66% {
        transform: translateY(-8px) scale(1.01) rotateX(-0.5deg);
        box-shadow:
            0 30px 70px rgba(0, 0, 0, 0.45),
            0 0 110px rgba(18, 194, 233, 0.2),
            inset 0 2px 0 rgba(255, 255, 255, 0.15);
        border-color: rgba(18, 194, 233, 0.25);
    }
}

/* 选项卡样式 */
.tabs {
    display: flex;
    margin: 40px 0;
    background: rgba(22, 33, 62, 0.8);
    border-radius: 30px;
    padding: 12px;
    border: 1px solid rgba(255, 255, 255, 0.12);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.tabs::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(255, 107, 157, 0.1) 0%,
        rgba(196, 113, 237, 0.1) 50%,
        rgba(18, 194, 233, 0.1) 100%);
    animation: tabGlow 8s ease-in-out infinite;
    pointer-events: none;
}

@keyframes tabGlow {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.6; }
}

.tab-button {
    flex: 1;
    background: none;
    border: none;
    padding: 20px 30px;
    cursor: pointer;
    font-size: 17px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 25px;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 1px;
    z-index: 1;
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Inter', sans-serif;
}

.tab-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.3) 0%,
        rgba(196, 113, 237, 0.3) 50%,
        rgba(18, 194, 233, 0.3) 100%);
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 25px;
}

.tab-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
    transition: all 0.4s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.tab-button:hover::before,
.tab-button.active::before {
    opacity: 1;
}

.tab-button:hover::after,
.tab-button.active::after {
    width: 120px;
    height: 120px;
}

.tab-button:hover,
.tab-button.active {
    color: rgba(255, 255, 255, 0.95);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 15px 40px rgba(255, 107, 157, 0.4),
        0 5px 20px rgba(196, 113, 237, 0.3);
}

/* 选项卡内容 */
.tab-content {
    display: none;
    animation: fadeIn 0.6s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 表单样式 */
.form-section {
    background: rgba(22, 33, 62, 0.6);
    padding: 40px;
    border-radius: 30px;
    margin: 30px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.05) 0%,
        rgba(196, 113, 237, 0.05) 100%);
    pointer-events: none;
}

.form-group {
    margin: 30px 0;
    position: relative;
}

label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2em;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Georgia', serif;
}

/* 输入框和选择框样式 */
input, select, textarea {
    width: 100%;
    padding: 20px 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 17px;
    font-weight: 500;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(15, 52, 96, 0.7);
    color: rgba(255, 255, 255, 0.9);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Inter', sans-serif;
}

/* 特殊处理select下拉框 */
select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255,255,255,0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 20px center;
    background-size: 20px;
    padding-right: 55px;
}

select option {
    background: rgba(26, 26, 46, 0.95);
    color: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    border: none;
    font-weight: 500;
}

select option:checked {
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.8) 0%,
        rgba(196, 113, 237, 0.8) 100%);
    color: rgba(255, 255, 255, 0.95);
}

input::placeholder, textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: rgba(255, 107, 157, 0.6);
    background: rgba(255, 255, 255, 0.12);
    box-shadow:
        0 0 0 4px rgba(255, 107, 157, 0.2),
        0 15px 35px rgba(255, 107, 157, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
}

/* 按钮样式 */
button {
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.8) 0%,
        rgba(196, 113, 237, 0.8) 50%,
        rgba(18, 194, 233, 0.8) 100%);
    color: rgba(255, 255, 255, 0.95);
    padding: 22px 40px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 18px;
    font-weight: 700;
    width: 100%;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 1.5px;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 15px 35px rgba(255, 107, 157, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    font-family: 'KaiTi', '楷体', '行楷', 'STKaiti', 'Inter', sans-serif;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.4) 50%,
        transparent 100%);
    transition: left 0.6s ease;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 25px 50px rgba(255, 107, 157, 0.5),
        0 10px 30px rgba(196, 113, 237, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg,
        rgba(255, 107, 157, 0.9) 0%,
        rgba(196, 113, 237, 0.9) 50%,
        rgba(18, 194, 233, 0.9) 100%);
}

button:active {
    transform: translateY(-2px) scale(0.98);
    transition: all 0.1s ease;
}

/* 歌单列表样式 */
.playlist-container {
    background: rgba(22, 33, 62, 0.6);
    border-radius: 30px;
    margin: 30px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 15px 35px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    overflow: hidden;
}

.playlist-header {
    padding: 20px 30px;
    background: rgba(15, 52, 96, 0.5);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    gap: 15px;
}

.select-all-btn, .download-btn {
    width: auto;
    padding: 12px 24px;
    font-size: 14px;
    margin: 0;
}

.playlist-items {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
}

.playlist-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    margin: 10px 0;
    background: rgba(15, 52, 96, 0.3);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.playlist-item:hover {
    background: rgba(255, 107, 157, 0.1);
    border-color: rgba(255, 107, 157, 0.3);
    transform: translateY(-2px);
}

.playlist-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 15px;
    cursor: pointer;
}

.playlist-info {
    flex: 1;
}

.playlist-name {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.playlist-artist {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}



/* 结果显示样式 */
.result-container {
    margin-top: 30px;
}

.result {
    padding: 25px 30px;
    border-radius: 20px;
    margin: 20px 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: resultFadeIn 0.5s ease-in-out;
}

@keyframes resultFadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.result.success {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
    color: rgba(255, 255, 255, 0.9);
}

.result.error {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
    color: rgba(255, 255, 255, 0.9);
}

.result.loading {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
    color: rgba(255, 255, 255, 0.9);
}

.result h3 {
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 700;
}

.result p {
    margin: 8px 0;
    line-height: 1.6;
}

/* 进度条样式 */
.progress-container {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg,
        rgba(255, 107, 157, 0.8) 0%,
        rgba(196, 113, 237, 0.8) 50%,
        rgba(18, 194, 233, 0.8) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    font-weight: 600;
    margin-bottom: 10px;
}

.progress-details {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .main-container {
        padding: 20px 10px;
        min-height: 100vh;
    }

    .container {
        padding: 24px 16px;
        margin: 0;
        border-radius: 20px;
        max-width: 100%;
    }

    h1 {
        font-size: 2.2em;
        margin-bottom: 15px;
    }

    .literary-quote {
        font-size: 1.1em;
        padding: 20px 16px;
        margin: 25px 0;
        border-radius: 16px;
    }

    .quote-text {
        margin-bottom: 12px;
        font-size: 0.95em;
    }

    .quote-author {
        font-size: 0.85em;
        line-height: 0.2;
    }

    .tabs {
        flex-direction: row;
        gap: 8px;
        padding: 8px;
        border-radius: 20px;
        margin: 25px 0;
    }

    .tab-button {
        padding: 16px 20px;
        font-size: 14px;
        border-radius: 16px;
        letter-spacing: 0.5px;
    }

    .form-section {
        padding: 20px 16px;
        border-radius: 20px;
        margin: 20px 0;
    }

    .form-group {
        margin: 20px 0;
    }

    label {
        font-size: 1.1em;
        margin-bottom: 10px;
    }

    input, select, textarea {
        padding: 16px 20px;
        font-size: 16px; /* 防止iOS缩放 */
        border-radius: 16px;
    }

    button {
        padding: 18px 32px;
        font-size: 16px;
        border-radius: 20px;
        letter-spacing: 1px;
    }

    .playlist-container {
        border-radius: 20px;
        margin: 20px 0;
    }

    .playlist-header {
        flex-direction: column;
        gap: 12px;
        padding: 16px 20px;
    }

    .select-all-btn, .download-btn {
        width: 100%;
        padding: 14px 24px;
        font-size: 15px;
    }

    .playlist-items {
        padding: 16px;
        max-height: 350px;
    }

    .playlist-item {
        padding: 12px 16px;
        margin: 8px 0;
        border-radius: 12px;
    }

    .playlist-name {
        font-size: 15px;
        margin-bottom: 4px;
    }

    .playlist-artist {
        font-size: 13px;
    }

    .result {
        padding: 20px 16px;
        border-radius: 16px;
        margin: 16px 0;
    }

    .result h3 {
        font-size: 1.2em;
        margin-bottom: 12px;
    }

    .result p {
        margin: 6px 0;
        line-height: 1.5;
    }

    .progress-container {
        margin: 16px 0;
    }

    .progress-bar {
        height: 6px;
        border-radius: 3px;
        margin-bottom: 8px;
    }

    .progress-text {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .progress-details {
        font-size: 13px;
    }
}

/* 小屏幕优化 */
@media (max-width: 480px) {
    .main-container {
        padding: 15px 8px;
    }

    .container {
        padding: 20px 12px;
        border-radius: 16px;
    }

    h1 {
        font-size: 1.9em;
        margin-bottom: 12px;
    }

    .literary-quote {
        font-size: 1.0em;
        padding: 16px 12px;
        margin: 20px 0;
        border-radius: 12px;
    }

    .quote-text {
        font-size: 0.9em;
        margin-bottom: 10px;
    }

    .quote-author {
        font-size: 0.8em;
    }

    .tabs {
        gap: 6px;
        padding: 6px;
        margin: 20px 0;
    }

    .tab-button {
        padding: 14px 16px;
        font-size: 13px;
        border-radius: 12px;
    }

    .form-section {
        padding: 16px 12px;
        border-radius: 16px;
    }

    input, select, textarea {
        padding: 14px 16px;
        font-size: 16px;
        border-radius: 12px;
    }

    button {
        padding: 16px 28px;
        font-size: 15px;
        border-radius: 16px;
    }

    .playlist-header {
        padding: 14px 16px;
    }

    .select-all-btn, .download-btn {
        padding: 12px 20px;
        font-size: 14px;
    }
}
