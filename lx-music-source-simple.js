/**
 * @name 偷听音乐源
 * @description 网易云音乐解析
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

// 简单的HTTP请求封装
const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// 音质映射
const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster'
  }
}

// API实现
const apis = {
  wy: {
    musicUrl(musicInfo, quality) {
      // 提取歌曲ID - 支持多种格式
      let songId = null
      if (musicInfo) {
        songId = musicInfo.songmid || musicInfo.id || musicInfo.songId || musicInfo.rid || musicInfo.hash
      }
      
      if (!songId) {
        return Promise.reject(new Error('无法提取歌曲ID'))
      }
      
      // 发送请求
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: String(songId),
          level: quality
        }),
        timeout: 15000
      }).then(responseBody => {
        // 解析响应
        const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
        
        if (data && data.status === 200 && data.url) {
          return data.url
        } else {
          throw new Error(data.error || '获取音乐链接失败')
        }
      })
    }
  }
}

// 事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  switch (action) {
    case 'musicUrl':
      if (apis[source] && apis[source].musicUrl) {
        return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
      }
      return Promise.reject(new Error('不支持的源'))
    default:
      return Promise.reject(new Error('不支持的操作'))
  }
})

// 初始化
send(EVENT_NAMES.inited, {
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})
