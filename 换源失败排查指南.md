# 🔍 落雪音乐"换源失败"问题排查指南

## 🎯 问题现状

- ❌ **换源失败** - 落雪音乐无法切换到自定义源
- ❌ **没有错误提示** - 没有明显的错误信息
- ❌ **脚本未被调用** - 自定义源脚本可能没有被正确加载

## 🔧 排查步骤

### **步骤1: 确认脚本加载**

#### **使用基础测试脚本**
1. 复制 `lx-music-test-basic.js` 的完整内容
2. 在落雪音乐中添加/替换自定义源
3. 保存并启用

#### **期望的控制台输出**
```
========== 脚本开始执行 ==========
时间: 2025/1/24 下午8:30:00
检查 globalThis.lx: object
LX API 可用
version: 2.0.0
env: desktop
API 解构成功
事件处理器注册完成
准备发送初始化事件...
✅ 初始化事件发送成功
========== 脚本初始化完成 ==========
如果看到这条消息，说明脚本加载成功
[心跳 1] 脚本正在运行...
```

#### **如果没有看到任何输出**
- 脚本没有被加载
- 可能是语法错误或格式问题

### **步骤2: 检查落雪音乐设置**

#### **自定义源管理**
1. 打开 **设置** → **自定义源**
2. 检查自定义源是否已添加
3. 确认自定义源状态为 **启用**
4. 查看是否有错误提示

#### **音乐源选择**
1. 在主界面检查当前选择的音乐源
2. 确认已切换到自定义源
3. 查看源列表中是否显示 "基础测试源"

### **步骤3: 检查落雪音乐版本**

#### **版本兼容性**
- **推荐版本**: 最新稳定版
- **最低要求**: 支持自定义源的版本
- **避免**: 过旧或开发版本

#### **检查方法**
1. 打开 **帮助** → **关于**
2. 查看版本号
3. 如果版本过旧，建议更新

### **步骤4: 检查脚本格式**

#### **常见格式问题**
1. **编码格式**: 必须是 UTF-8
2. **文件完整性**: 确保脚本内容完整
3. **语法错误**: 检查是否有语法错误
4. **注释格式**: 确保头部注释格式正确

#### **正确的头部格式**
```javascript
/**
 * @name 基础测试源
 * @description 测试脚本是否被正确加载
 * @version 1.0.0
 * <AUTHOR>
 */
```

### **步骤5: 手动验证环境**

#### **在控制台中执行**
如果落雪音乐支持控制台，尝试手动执行：

```javascript
// 检查环境
console.log('globalThis.lx:', globalThis.lx)

// 检查API
if (globalThis.lx) {
  console.log('version:', globalThis.lx.version)
  console.log('EVENT_NAMES:', globalThis.lx.EVENT_NAMES)
}
```

### **步骤6: 重启和清理**

#### **清理缓存**
1. 删除所有自定义源
2. 重启落雪音乐
3. 重新添加基础测试脚本

#### **完全重置**
1. 备份设置
2. 重置落雪音乐设置
3. 重新配置自定义源

## 🚨 常见问题和解决方案

### **问题A: 脚本语法错误**
**症状**: 添加脚本后没有任何反应
**解决**: 
- 检查脚本语法
- 使用基础测试脚本验证
- 确保头部注释格式正确

### **问题B: 落雪音乐版本不兼容**
**症状**: 自定义源功能不可用
**解决**:
- 更新到最新版本
- 查看官方文档确认版本要求

### **问题C: 权限或安全限制**
**症状**: 脚本加载但无法执行网络请求
**解决**:
- 检查防火墙设置
- 确认网络权限
- 尝试使用管理员权限运行

### **问题D: 源配置错误**
**症状**: 脚本加载但不显示在源列表中
**解决**:
- 检查 `send(EVENT_NAMES.inited, ...)` 的格式
- 确认 `sources` 对象结构正确
- 验证 `qualitys` 数组格式

## 📋 调试检查清单

- [ ] 脚本能够正常加载（看到控制台输出）
- [ ] 初始化事件发送成功
- [ ] 自定义源显示在源列表中
- [ ] 能够切换到自定义源
- [ ] 落雪音乐版本兼容
- [ ] 网络权限正常
- [ ] 脚本格式正确

## 🎯 下一步

1. **先使用基础测试脚本** - 确认环境正常
2. **逐步增加功能** - 从简单到复杂
3. **详细记录日志** - 便于问题定位
4. **分享具体错误信息** - 如果有的话

---

**如果基础测试脚本都无法工作，问题可能在落雪音乐的环境或配置上，而不是我们的脚本逻辑。**
