/**
 * UI控制模块 - 处理用户界面交互和显示
 */

class UIController {
    constructor() {
        this.currentTab = 'single';
        this.currentPlaylistSongs = [];
        this.resultContainer = null;
        
        this.init();
    }

    /**
     * 初始化UI控制器
     */
    init() {
        this.resultContainer = document.getElementById('result');
        this.setupEventListeners();
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 回车键支持
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'singleInput') {
                    this.parseSingle();
                } else if (activeElement.id === 'batchInput') {
                    this.loadPlaylist();
                }
            }
        });

        // 设置API进度回调
        if (window.musicAPI) {
            window.musicAPI.setProgressCallback((progress) => {
                this.updateDownloadProgress(progress);
            });
        }
    }

    /**
     * 切换选项卡
     */
    showTab(tabName) {
        // 隐藏所有选项卡内容
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });

        // 移除所有按钮的active类
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.classList.remove('active');
        });

        // 显示选中的选项卡
        const selectedTab = document.getElementById(tabName);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }

        // 激活对应的按钮
        const selectedButton = document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedButton) {
            selectedButton.classList.add('active');
        }

        this.currentTab = tabName;
        this.clearResult();
    }

    /**
     * 自动解析输入内容
     */
    autoParseInput(type) {
        const inputElement = document.getElementById(type === 'single' ? 'singleInput' : 'batchInput');
        const inputValue = inputElement.value.trim();
        
        if (!inputValue) {
            this.clearResult();
            return;
        }

        const result = window.musicParser.parseInput(inputValue);
        
        if (result) {
            const formatted = window.musicParser.formatResult(result);
            if (formatted) {
                this.showParseResult(formatted);
                
                // 如果是批量下载，自动设置类型
                if (type === 'batch' && (formatted.type === 'playlist' || formatted.type === 'album')) {
                    const batchTypeSelect = document.getElementById('batchType');
                    batchTypeSelect.value = formatted.type;
                    
                    // 更新输入框为解析出的ID
                    inputElement.value = formatted.id;
                }
            }
        }
    }

    /**
     * 显示解析结果
     */
    showParseResult(result) {
        const typeName = window.musicParser.getTypeName(result.type);
        const extractedInfo = result.extractedInfo;
        
        let infoText = '';
        if (extractedInfo.songName) {
            infoText += `歌曲：${extractedInfo.songName}<br>`;
        }
        if (extractedInfo.artist) {
            infoText += `艺术家：${extractedInfo.artist}<br>`;
        }
        if (extractedInfo.albumName) {
            infoText += `专辑：${extractedInfo.albumName}<br>`;
        }
        if (extractedInfo.playlistName) {
            infoText += `歌单：${extractedInfo.playlistName}<br>`;
        }

        this.showResult('success', `
            <h3>🎯 自动识别成功</h3>
            <p>检测到${typeName}ID: ${result.id}</p>
            ${infoText ? `<div style="margin-top: 10px;">${infoText}</div>` : ''}
            <p style="margin-top: 10px;">点击相应按钮继续操作</p>
        `);
    }

    /**
     * 显示结果
     */
    showResult(type, content) {
        if (!this.resultContainer) return;
        
        this.resultContainer.innerHTML = `
            <div class="result ${type}">
                ${content}
            </div>
        `;
    }

    /**
     * 清除结果显示
     */
    clearResult() {
        if (this.resultContainer) {
            this.resultContainer.innerHTML = '';
        }
    }

    /**
     * 显示加载状态
     */
    showLoading(message) {
        this.showResult('loading', `
            <h3>🔄 ${message}</h3>
        `);
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.showResult('error', `
            <h3>❌ 操作失败</h3>
            <p>${message}</p>
        `);
    }

    /**
     * 显示成功信息
     */
    showSuccess(content) {
        this.showResult('success', content);
    }

    /**
     * 显示歌单列表
     */
    showPlaylist(data) {
        const container = document.getElementById('playlistContainer');
        const itemsContainer = document.getElementById('playlistItems');

        if (!container || !itemsContainer) return;

        // 显示歌单信息
        this.showSuccess(`
            <h3>📋 ${data.name}</h3>
            <p>共 ${data.songs.length} 首歌曲</p>
        `);

        // 生成歌曲列表
        let itemsHtml = '';
        data.songs.forEach((song) => {
            itemsHtml += `
                <div class="playlist-item selected" onclick="toggleSongSelection(${song.id})">
                    <input type="checkbox" class="playlist-checkbox" id="song_${song.id}" checked>
                    <div class="checkbox-custom"></div>
                    <div class="playlist-info">
                        <div class="playlist-name">${song.name}</div>
                        <div class="playlist-artist">${song.artist}</div>
                    </div>
                </div>
            `;
        });

        itemsContainer.innerHTML = itemsHtml;
        container.style.display = 'block';

        // 存储歌曲数据
        this.currentPlaylistSongs = data.songs;
    }

    /**
     * 更新下载进度
     */
    updateDownloadProgress(progress) {
        const { current, total, song, success, failed } = progress;
        const percentage = Math.round((current / total) * 100);

        this.showResult('loading', `
            <h3>🎵 批量下载进行中</h3>
            <div class="progress-container">
                <div class="progress-text">正在下载第 ${current} 首，共 ${total} 首 (${percentage}%)</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${percentage}%"></div>
                    <div class="progress-percentage">${percentage}%</div>
                </div>
                <div class="progress-details">
                    <span>进度: ${current} / ${total}</span>
                    <span>成功: ${success} | 失败: ${failed}</span>
                </div>
            </div>
            <p style="margin-top: 15px; padding: 12px; background: rgba(255, 107, 157, 0.1); border-radius: 12px; border: 1px solid rgba(255, 107, 157, 0.3);">
                🎵 ${song.artist} - ${song.name}
            </p>
        `);
    }

    /**
     * 显示下载完成结果
     */
    showDownloadComplete(results) {
        const { successCount, failCount, results: downloadResults } = results;
        const total = successCount + failCount;
        const successRate = Math.round((successCount / total) * 100);

        let resultHtml = `
            <h3>🎉 批量下载完成</h3>
            <div class="progress-container">
                <div class="progress-text">✅ 下载任务完成 - 成功率 ${successRate}%</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%"></div>
                    <div class="progress-percentage">100%</div>
                </div>
                <div class="progress-details">
                    <span>总计: ${total} 首</span>
                    <span>成功: ${successCount} | 失败: ${failCount}</span>
                </div>
            </div>
        `;

        if (failCount > 0) {
            resultHtml += `
                <div style="margin-top: 20px; padding: 15px; background: rgba(239, 68, 68, 0.1); border-radius: 15px; border: 1px solid rgba(239, 68, 68, 0.3);">
                    <h4 style="color: rgba(255, 255, 255, 0.9); margin-bottom: 10px;">❌ 失败的歌曲 (${failCount} 首)：</h4>
            `;
            downloadResults.filter(r => r.status === 'failed').forEach(result => {
                resultHtml += `
                    <p style="color: rgba(255, 255, 255, 0.8); margin: 8px 0; padding: 8px; background: rgba(0, 0, 0, 0.2); border-radius: 8px;">
                        🚫 ${result.artist} - ${result.name}
                        <span style="color: rgba(239, 68, 68, 0.8); font-size: 13px;">(${result.error})</span>
                    </p>
                `;
            });
            resultHtml += '</div>';
        }

        this.showSuccess(resultHtml);
    }

    /**
     * 获取选中的歌曲
     */
    getSelectedSongs() {
        const checkboxes = document.querySelectorAll('.playlist-checkbox:checked');
        return Array.from(checkboxes).map(cb => {
            const songId = cb.id.replace('song_', '');
            return this.currentPlaylistSongs.find(song => song.id.toString() === songId);
        }).filter(song => song);
    }

    /**
     * 切换全选状态
     */
    toggleSelectAll() {
        const checkboxes = document.querySelectorAll('.playlist-checkbox');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
            const playlistItem = cb.closest('.playlist-item');
            if (playlistItem) {
                if (cb.checked) {
                    playlistItem.classList.add('selected');
                } else {
                    playlistItem.classList.remove('selected');
                }
            }
        });
    }
}

// 创建全局UI控制器实例
window.uiController = new UIController();
