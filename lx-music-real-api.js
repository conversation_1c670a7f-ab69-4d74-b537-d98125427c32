/**
 * @name 偷听音乐源
 * @description 使用真实API的版本
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[偷听音源] 脚本启动')

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

const apis = {
  wy: {
    musicUrl({ songmid, id, songId, rid }, quality) {
      console.log('[偷听音源] 开始处理请求')
      console.log('[偷听音源] 参数:', { songmid, id, songId, rid, quality })
      
      // 提取歌曲ID，如果没有就使用默认测试ID
      const songIdValue = songmid || id || songId || rid || '1901371647'
      console.log('[偷听音源] 使用歌曲ID:', songIdValue)
      
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: String(songIdValue),
          level: quality || 'lossless'
        }),
        timeout: 15000
      }).then(responseBody => {
        console.log('[偷听音源] API响应:', responseBody)
        
        const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
        
        if (data && data.status === 200 && data.url) {
          console.log('[偷听音源] ✅ 获取到URL:', data.url)
          console.log('[偷听音源] 歌曲信息:', data.name, '-', data.ar_name)
          
          // 直接返回API提供的URL
          return data.url
        } else {
          console.error('[偷听音源] API错误:', data.error || '未知错误')
          throw new Error(data.error || '获取音乐链接失败')
        }
      }).catch(error => {
        console.error('[偷听音源] 请求失败:', error.message)
        throw error
      })
    },
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] 收到请求:', { source, action })
  
  switch (action) {
    case 'musicUrl':
      const quality = qualitys[source][info.type]
      console.log('[偷听音源] 音质映射:', info.type, '->', quality)
      return apis[source].musicUrl(info.musicInfo, quality)
    default:
      return Promise.reject(new Error('不支持的操作'))
  }
})

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[偷听音源] 初始化完成')
