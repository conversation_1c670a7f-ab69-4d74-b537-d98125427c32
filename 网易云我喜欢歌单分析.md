# 🔍 网易云"我喜欢的歌单"Token注入机制分析

## 📋 问题背景

您遇到的问题：获取网易云"我喜欢的歌单"时需要token才能访问，而您提供的落雪音乐格式包含了特殊的token。

## 🔗 URL格式分析

### **您提供的落雪音乐格式：**
```
https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048###0040C48D8B74947D362882BF647D1888CE71C666AF3336A16B3E5878186D4F0AB78B86B743A7301F7B951B0D66251E627CFD79B352C7A375D236233424FA8C54212D2682848B2F635BE6DE85A831F7F9DF94100924E04CBAA477B8ED482553FE81554DA1D3C0388FC91D46AD94549695EF0E5108A858297AC6004678AC4F731A657B3477D656AB1F5CC0086A2C6941783402EB85BC3DECE183E1150193696B7F2B733DFE1ABFA9F0DECEB8838D46A74BBA0A364D78A72F446454792FA4AB58B5D08673A42749D80F50C058BA0EB8A85D53F574F25FBF4CB92856A98BDB599AF104D7224EF8221A536C2660714941E9A785F372D56D63FF77FFAD9DFA93B5EDED584CAABAD1A92D548D8952B6BF145CBA8DC8F1932B25301E8A4F98052A6DE21035283AE3E61045ECA455C6329D6119E7BCA3C7A3E03D0049EB99467595634FE8D908209985370FE9CD46F4672C6666FE517AA20873D93EA10E9DC763461B87E57C
```

### **格式解析：**
```
[基础URL]###[MUSIC_U Token]
```

**基础URL部分：**
- `https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048`
- 歌单ID: `2213306925`
- 创建者ID: `1448699048`

**Token部分：**
- 分隔符: `###`
- MUSIC_U值: `0040C48D8B74947D362882BF647D1888CE71C666AF3336A16B3E5878186D4F0AB78B86B743A7301F7B951B0D66251E627CFD79B352C7A375D236233424FA8C54212D2682848B2F635BE6DE85A831F7F9DF94100924E04CBAA477B8ED482553FE81554DA1D3C0388FC91D46AD94549695EF0E5108A858297AC6004678AC4F731A657B3477D656AB1F5CC0086A2C6941783402EB85BC3DECE183E1150193696B7F2B733DFE1ABFA9F0DECEB8838D46A74BBA0A364D78A72F446454792FA4AB58B5D08673A42749D80F50C058BA0EB8A85D53F574F25FBF4CB92856A98BDB599AF104D7224EF8221A536C2660714941E9A785F372D56D63FF77FFAD9DFA93B5EDED584CAABAD1A92D548D8952B6BF145CBA8DC8F1932B25301E8A4F98052A6DE21035283AE3E61045ECA455C6329D6119E7BCA3C7A3E03D0049EB99467595634FE8D908209985370FE9CD46F4672C6666FE517AA20873D93EA10E9DC763461B87E57C`

## 🔧 落雪音乐的Token注入机制

根据落雪音乐官方文档，token注入的工作原理：

### **1. 格式规范**
```
[歌单链接或ID]###[MUSIC_U Token]
```

### **2. 支持的格式示例**
```javascript
// 完整URL + Token
"https://music.163.com/#/playlist?id=11332&userid=123456###xxxxxx"

// 仅歌单ID + Token  
"11332###xxxxxx"

// 您的格式（移动端URL + Token）
"https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048###xxxxxx"
```

### **3. Token获取方法**
1. 浏览器登录网易云音乐
2. F12打开开发者工具
3. Application/Storage → Cookies → https://music.163.com
4. 找到名称为 `MUSIC_U` 的Cookie值

## 🎯 在您的API中实现Token支持

### **第一步：解析Token格式**

```typescript
interface PlaylistRequest {
  url: string;
  token?: string;
}

function parsePlaylistUrl(input: string): PlaylistRequest {
  // 检查是否包含token
  if (input.includes('###')) {
    const [url, token] = input.split('###');
    return { url: url.trim(), token: token.trim() };
  }
  
  return { url: input.trim() };
}
```

### **第二步：提取歌单信息**

```typescript
function extractPlaylistInfo(url: string) {
  // 支持多种URL格式
  const patterns = [
    /playlist\?id=(\d+)/,           // 标准格式
    /m\/playlist\?id=(\d+)/,        // 移动端格式
    /playlist\/(\d+)/               // 简化格式
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return {
        playlistId: match[1],
        creatorId: extractCreatorId(url)
      };
    }
  }
  
  // 如果是纯数字，直接作为歌单ID
  if (/^\d+$/.test(url)) {
    return { playlistId: url };
  }
  
  throw new Error('无法解析歌单URL');
}

function extractCreatorId(url: string): string | undefined {
  const match = url.match(/creatorId=(\d+)/);
  return match ? match[1] : undefined;
}
```

### **第三步：使用Token请求歌单**

```typescript
async function getPlaylistWithToken(playlistId: string, token?: string) {
  const cookies: CookieObject = {};
  
  // 如果提供了token，添加到cookies中
  if (token) {
    cookies.MUSIC_U = token;
    // 可能还需要其他必要的cookies
    cookies.os = 'pc';
    cookies.appver = '8.9.75';
  }
  
  // 使用您现有的歌单获取方法
  return await this.getPlaylistDetail(playlistId, cookies);
}
```

## 🛠️ 修改您的API接口

### **在main.ts中添加歌单解析支持**

```typescript
// 添加新的歌单解析接口
app.post('/api/playlist', async (req) => {
  try {
    const { url } = req.body;
    
    if (!url) {
      return new Response(JSON.stringify({
        status: 400,
        error: '缺少歌单URL参数'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
    
    // 解析URL和token
    const { url: playlistUrl, token } = parsePlaylistUrl(url);
    const { playlistId, creatorId } = extractPlaylistInfo(playlistUrl);
    
    // 构建cookies
    const cookies: CookieObject = {};
    if (token) {
      cookies.MUSIC_U = token;
    } else if (config.NETEASE_COOKIE) {
      // 使用环境变量中的cookie
      const cookiePairs = config.NETEASE_COOKIE.split(';');
      for (const pair of cookiePairs) {
        const [key, value] = pair.split('=').map(s => s.trim());
        if (key && value) {
          cookies[key] = value;
        }
      }
    }
    
    // 获取歌单详情
    const playlistDetail = await neteaseAPI.getPlaylistDetail(playlistId, cookies);
    
    return new Response(JSON.stringify({
      status: 200,
      data: playlistDetail
    }), {
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
    
  } catch (error) {
    console.error('歌单解析失败:', error);
    return new Response(JSON.stringify({
      status: 500,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
});
```

## 🎵 前端集成

### **在您的网页中添加歌单解析功能**

```javascript
// 添加歌单解析函数
async function parsePlaylist() {
  const playlistUrl = document.getElementById('playlistUrl').value.trim();
  
  if (!playlistUrl) {
    alert('请输入歌单链接');
    return;
  }
  
  try {
    showLoading('正在解析歌单...');
    
    const response = await fetch('/api/playlist', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ url: playlistUrl })
    });
    
    const data = await response.json();
    
    if (data.status === 200) {
      displayPlaylist(data.data);
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('歌单解析失败:', error);
    alert(`歌单解析失败: ${error.message}`);
  } finally {
    hideLoading();
  }
}
```

## 🎯 总结

1. **Token格式**：`URL###MUSIC_U_VALUE`
2. **关键点**：MUSIC_U是用户登录后的身份令牌
3. **实现方案**：在API中解析token并添加到请求cookies中
4. **安全性**：Token是敏感信息，需要妥善处理

这样您就可以支持落雪音乐的"我喜欢的歌单"格式，并在您的API中正确解析和使用token了！
