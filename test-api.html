<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - 落雪音乐适配</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        input { width: 300px; padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .log { max-height: 300px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>🎵 API测试 - 落雪音乐适配</h1>
    
    <div class="test-section info">
        <h3>📊 服务状态检查</h3>
        <button onclick="checkStatus()">检查API状态</button>
        <div id="statusResult"></div>
    </div>
    
    <div class="test-section">
        <h3>🎵 单曲解析测试</h3>
        <input type="text" id="songId" placeholder="输入歌曲ID (例如: 1901371647)" value="1901371647">
        <select id="quality">
            <option value="standard">标准音质</option>
            <option value="lossless" selected>无损音质</option>
            <option value="hires">Hi-Res音质</option>
            <option value="jymaster">超清母带</option>
        </select>
        <button onclick="testSong()">测试解析</button>
        <div id="songResult"></div>
    </div>
    
    <div class="test-section">
        <h3>🔧 落雪音乐格式测试</h3>
        <p>模拟落雪音乐传递的数据格式：</p>
        <button onclick="testLXFormat()">测试LX格式</button>
        <div id="lxResult"></div>
    </div>
    
    <div class="test-section">
        <h3>📝 调试日志</h3>
        <button onclick="clearLog()">清空日志</button>
        <div id="debugLog" class="log"></div>
    </div>

    <script>
        const API_BASE = 'https://suwyy.deno.dev';
        
        function log(message) {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }
        
        async function checkStatus() {
            const resultDiv = document.getElementById('statusResult');
            log('🔍 检查API状态...');
            
            try {
                const response = await fetch(`${API_BASE}/api/status`);
                const data = await response.json();
                
                if (data.status === 200) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 服务正常</h4>
                            <p><strong>服务:</strong> ${data.service}</p>
                            <p><strong>版本:</strong> ${data.version}</p>
                            <p><strong>环境:</strong> ${data.environment}</p>
                            <p><strong>Cookie配置:</strong> ${data.cookieConfigured ? '✅ 已配置' : '❌ 未配置'}</p>
                            <p><strong>消息:</strong> ${data.message}</p>
                        </div>
                    `;
                    log('✅ API状态检查成功');
                } else {
                    throw new Error('API返回错误状态');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 状态检查失败: ${error.message}</div>`;
                log(`❌ API状态检查失败: ${error.message}`);
            }
        }
        
        async function testSong() {
            const songId = document.getElementById('songId').value;
            const quality = document.getElementById('quality').value;
            const resultDiv = document.getElementById('songResult');
            
            if (!songId) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入歌曲ID</div>';
                return;
            }
            
            log(`🎵 测试歌曲解析: ID=${songId}, 音质=${quality}`);
            resultDiv.innerHTML = '<div class="info">🔄 正在解析...</div>';
            
            try {
                const requestData = {
                    ids: songId,
                    level: quality
                };
                
                log(`📤 发送请求: ${JSON.stringify(requestData)}`);
                
                const response = await fetch(`${API_BASE}/api/song`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📥 收到响应: 状态码=${response.status}`);
                
                const data = await response.json();
                log(`📊 响应数据: ${JSON.stringify(data, null, 2)}`);
                
                if (data.status === 200 && data.url) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 解析成功</h4>
                            <p><strong>歌曲:</strong> ${data.name}</p>
                            <p><strong>歌手:</strong> ${data.ar_name}</p>
                            <p><strong>专辑:</strong> ${data.al_name}</p>
                            <p><strong>音质:</strong> ${data.level}</p>
                            <p><strong>大小:</strong> ${data.size}</p>
                            <p><strong>比特率:</strong> ${data.br}</p>
                            <p><strong>URL:</strong> <a href="${data.url}" target="_blank">🎵 播放链接</a></p>
                            ${data.note ? `<p><strong>备注:</strong> ${data.note}</p>` : ''}
                        </div>
                    `;
                    log('✅ 歌曲解析成功');
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ 解析失败: ${data.error || '未知错误'}</div>`;
                    log(`❌ 歌曲解析失败: ${data.error || '未知错误'}`);
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 请求失败: ${error.message}</div>`;
                log(`❌ 请求失败: ${error.message}`);
            }
        }
        
        async function testLXFormat() {
            const resultDiv = document.getElementById('lxResult');
            log('🔧 测试落雪音乐数据格式...');
            
            // 模拟落雪音乐可能传递的数据格式
            const mockMusicInfo = {
                songmid: '1901371647',
                id: '1901371647',
                name: '测试歌曲',
                singer: '测试歌手',
                album: '测试专辑'
            };
            
            const mockQuality = '320k';
            
            log(`🎭 模拟数据: musicInfo=${JSON.stringify(mockMusicInfo)}, quality=${mockQuality}`);
            
            // 模拟自定义源的处理逻辑
            try {
                // 提取歌曲ID
                const songId = mockMusicInfo.songmid || mockMusicInfo.id;
                
                // 音质映射
                const qualityMap = {
                    '128k': 'standard',
                    '320k': 'lossless',
                    'flac': 'hires',
                    'flac24bit': 'jymaster'
                };
                const apiQuality = qualityMap[mockQuality] || 'lossless';
                
                log(`🎯 提取ID: ${songId}, 音质映射: ${mockQuality} -> ${apiQuality}`);
                
                // 发送请求
                const requestData = { ids: songId, level: apiQuality };
                const response = await fetch(`${API_BASE}/api/song`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.status === 200 && data.url) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 落雪音乐格式测试成功</h4>
                            <p><strong>输入格式:</strong> ${JSON.stringify(mockMusicInfo)}</p>
                            <p><strong>提取ID:</strong> ${songId}</p>
                            <p><strong>音质映射:</strong> ${mockQuality} → ${apiQuality}</p>
                            <p><strong>返回URL:</strong> ${data.url}</p>
                            <p><strong>✅ 符合落雪音乐要求</strong></p>
                        </div>
                    `;
                    log('✅ 落雪音乐格式测试成功');
                } else {
                    throw new Error(data.error || '解析失败');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 格式测试失败: ${error.message}</div>`;
                log(`❌ 格式测试失败: ${error.message}`);
            }
        }
        
        // 页面加载时自动检查状态
        window.onload = function() {
            log('🚀 页面加载完成，开始测试...');
            checkStatus();
        };
    </script>
</body>
</html>
