/**
 * @name 偷听音乐源(URL修复版)
 * @description 专门解决URL播放问题
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[URL修复] 脚本启动')

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// URL处理函数
function processUrl(originalUrl) {
  console.log('[URL修复] 原始URL:', originalUrl)
  
  if (!originalUrl) {
    throw new Error('URL为空')
  }
  
  let finalUrl = originalUrl
  
  // 确保使用HTTPS
  if (finalUrl.startsWith('http://')) {
    finalUrl = finalUrl.replace('http://', 'https://')
    console.log('[URL修复] 转换为HTTPS:', finalUrl)
  }
  
  // 检查URL格式
  if (!finalUrl.startsWith('http')) {
    throw new Error('无效的URL格式: ' + finalUrl)
  }
  
  // 如果是网易云的URL，可能需要特殊处理
  if (finalUrl.includes('music.126.net')) {
    console.log('[URL修复] 检测到网易云URL')
    
    // 移除可能的重定向参数
    if (finalUrl.includes('?')) {
      const urlParts = finalUrl.split('?')
      const baseUrl = urlParts[0]
      const params = urlParts[1]
      
      // 保留重要参数，移除可能有问题的参数
      const validParams = params.split('&').filter(param => {
        return param.startsWith('vuutv=') || param.startsWith('auth=')
      })
      
      if (validParams.length > 0) {
        finalUrl = baseUrl + '?' + validParams.join('&')
      } else {
        finalUrl = baseUrl
      }
      
      console.log('[URL修复] 参数处理后:', finalUrl)
    }
  }
  
  console.log('[URL修复] 最终URL:', finalUrl)
  return finalUrl
}

const apis = {
  wy: {
    musicUrl({ songmid, id, songId, rid }, quality) {
      console.log('[URL修复] ========== 开始处理 ==========')
      console.log('[URL修复] 参数:', { songmid, id, songId, rid, quality })
      
      const songIdValue = songmid || id || songId || rid || '1901371647'
      console.log('[URL修复] 使用歌曲ID:', songIdValue)
      
      const requestData = {
        ids: String(songIdValue),
        level: quality || 'lossless'
      }
      
      console.log('[URL修复] 发送API请求:', requestData)
      
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        },
        body: JSON.stringify(requestData),
        timeout: 20000
      }).then(responseBody => {
        console.log('[URL修复] 收到响应:', responseBody)
        
        try {
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          
          if (data && data.status === 200 && data.url) {
            console.log('[URL修复] API成功返回')
            console.log('[URL修复] 歌曲:', data.name || '未知')
            console.log('[URL修复] 歌手:', data.ar_name || '未知')
            console.log('[URL修复] 音质:', data.level || '未知')
            console.log('[URL修复] 大小:', data.size || '未知')
            
            // 处理URL
            const finalUrl = processUrl(data.url)
            
            console.log('[URL修复] ✅ 返回处理后的URL')
            return finalUrl
            
          } else {
            const error = (data && data.error) || '获取音乐链接失败'
            console.error('[URL修复] API错误:', error)
            throw new Error(error)
          }
        } catch (parseError) {
          console.error('[URL修复] 解析错误:', parseError.message)
          throw new Error('响应解析失败')
        }
      }).catch(error => {
        console.error('[URL修复] 请求失败:', error.message)
        
        // 提供更友好的错误信息
        if (error.message.includes('timeout')) {
          throw new Error('网络超时，请稍后重试')
        } else if (error.message.includes('ENOTFOUND')) {
          throw new Error('无法连接到服务器')
        } else {
          throw error
        }
      })
    },
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[URL修复] 收到请求:', { source, action })
  
  switch (action) {
    case 'musicUrl':
      if (!apis[source]) {
        return Promise.reject(new Error('不支持的源: ' + source))
      }
      
      if (!qualitys[source] || !qualitys[source][info.type]) {
        return Promise.reject(new Error('不支持的音质: ' + info.type))
      }
      
      const quality = qualitys[source][info.type]
      console.log('[URL修复] 音质映射:', info.type, '->', quality)
      
      return apis[source].musicUrl(info.musicInfo, quality).catch(err => {
        console.error('[URL修复] 最终错误:', err.message)
        return Promise.reject(err)
      })
    
    default:
      return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源(URL修复版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[URL修复] ========== 初始化完成 ==========')
console.log('[URL修复] 特性: URL格式修复、HTTPS转换、参数清理')
console.log('[URL修复] 准备就绪')
