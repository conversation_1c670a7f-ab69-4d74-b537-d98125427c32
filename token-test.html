<!DOCTYPE html>
<html>
<head>
    <title>Token解析测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        input { width: 100%; padding: 8px; margin: 5px 0; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Token解析测试</h1>
    
    <div class="test-case">
        <h3>测试1: 落雪音乐Token格式</h3>
        <input type="text" id="tokenInput" value="https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048###0040C48D8B74947D362882BF647D1888CE71C666AF3336A16B3E5878186D4F0AB78B86B743A7301F7B951B0D66251E627CFD79B352C7A375D236233424FA8C54212D2682848B2F635BE6DE85A831F7F9DF94100924E04CBAA477B8ED482553FE81554DA1D3C0388FC91D46AD94549695EF0E5108A858297AC6004678AC4F731A657B3477D656AB1F5CC0086A2C6941783402EB85BC3DECE183E1150193696B7F2B733DFE1ABFA9F0DECEB8838D46A74BBA0A364D78A72F446454792FA4AB58B5D08673A42749D80F50C058BA0EB8A85D53F574F25FBF4CB92856A98BDB599AF104D7224EF8221A536C2660714941E9A785F372D56D63FF77FFAD9DFA93B5EDED584CAABAD1A92D548D8952B6BF145CBA8DC8F1932B25301E8A4F98052A6DE21035283AE3E61045ECA455C6329D6119E7BCA3C7A3E03D0049EB99467595634FE8D908209985370FE9CD46F4672C6666FE517AA20873D93EA10E9DC763461B87E57C">
        <button onclick="testTokenParsing()">测试Token解析</button>
        <div id="tokenResult" class="result"></div>
    </div>
    
    <div class="test-case">
        <h3>测试2: 普通歌单格式</h3>
        <input type="text" id="normalInput" value="https://music.163.com/playlist?id=60198">
        <button onclick="testNormalParsing()">测试普通解析</button>
        <div id="normalResult" class="result"></div>
    </div>
    
    <div class="test-case">
        <h3>测试3: API调用</h3>
        <button onclick="testAPICall()">测试API调用</button>
        <div id="apiResult" class="result"></div>
    </div>

    <script>
        // 从URL中提取歌单ID
        function extractPlaylistId(url) {
            const patterns = [
                /playlist\?id=(\d+)/,           // 标准格式
                /m\/playlist\?id=(\d+)/,        // 移动端格式  
                /playlist\/(\d+)/               // 简化格式
            ];
            
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match) {
                    return match[1];
                }
            }
            
            // 如果是纯数字，直接作为歌单ID
            if (/^\d+$/.test(url)) {
                return url;
            }
            
            return null;
        }
        
        function testTokenParsing() {
            const input = document.getElementById('tokenInput').value;
            const result = document.getElementById('tokenResult');
            
            console.log('🔍 测试Token解析:', input);
            
            if (input.includes('###')) {
                const [urlPart, tokenPart] = input.split('###');
                const playlistId = extractPlaylistId(urlPart.trim());
                const userToken = tokenPart.trim();
                
                const parseResult = {
                    hasToken: true,
                    urlPart: urlPart.trim(),
                    playlistId: playlistId,
                    tokenLength: userToken.length,
                    tokenPreview: userToken.substring(0, 50) + '...'
                };
                
                result.innerHTML = `
                    <h4>✅ Token解析成功</h4>
                    <p><strong>URL部分:</strong> ${parseResult.urlPart}</p>
                    <p><strong>歌单ID:</strong> ${parseResult.playlistId}</p>
                    <p><strong>Token长度:</strong> ${parseResult.tokenLength}</p>
                    <p><strong>Token预览:</strong> ${parseResult.tokenPreview}</p>
                    <p><strong>构建的请求体:</strong></p>
                    <pre>${JSON.stringify({
                        type: 'playlist',
                        id: parseResult.playlistId,
                        token: userToken
                    }, null, 2)}</pre>
                `;
                
                console.log('📋 解析结果:', parseResult);
            } else {
                result.innerHTML = '<h4>❌ 未检测到Token格式</h4>';
            }
        }
        
        function testNormalParsing() {
            const input = document.getElementById('normalInput').value;
            const result = document.getElementById('normalResult');
            
            const playlistId = extractPlaylistId(input);
            
            if (playlistId) {
                const parseResult = {
                    hasToken: false,
                    playlistId: playlistId,
                    requestBody: {
                        type: 'playlist',
                        id: playlistId
                    }
                };
                
                result.innerHTML = `
                    <h4>✅ 普通解析成功</h4>
                    <p><strong>歌单ID:</strong> ${parseResult.playlistId}</p>
                    <p><strong>构建的请求体:</strong></p>
                    <pre>${JSON.stringify(parseResult.requestBody, null, 2)}</pre>
                `;
            } else {
                result.innerHTML = '<h4>❌ 无法解析歌单ID</h4>';
            }
        }
        
        async function testAPICall() {
            const result = document.getElementById('apiResult');
            result.innerHTML = '<p>🔄 正在测试API调用...</p>';
            
            try {
                // 测试Token格式
                const tokenInput = document.getElementById('tokenInput').value;
                const [urlPart, tokenPart] = tokenInput.split('###');
                const playlistId = extractPlaylistId(urlPart.trim());
                
                const requestBody = {
                    type: 'playlist',
                    id: playlistId,
                    token: tokenPart.trim()
                };
                
                console.log('📡 发送API请求:', requestBody);
                
                const response = await fetch('/api/playlist-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const data = await response.json();
                
                result.innerHTML = `
                    <h4>📡 API响应</h4>
                    <p><strong>状态码:</strong> ${response.status}</p>
                    <p><strong>响应数据:</strong></p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
                console.log('📡 API响应:', data);
                
            } catch (error) {
                result.innerHTML = `
                    <h4>❌ API调用失败</h4>
                    <p><strong>错误:</strong> ${error.message}</p>
                `;
                console.error('API调用失败:', error);
            }
        }
    </script>
</body>
</html>
