// 网易云音乐解析器 - 重构版服务器
// 基于Deno的现代化模块架构

import { NeteaseEAPI, QualityLevel, SongInfo } from './netease-eapi.ts';

// 环境检测
const isDenoDeploy = !!Deno.env.get('DENO_DEPLOYMENT_ID');

// 配置管理
interface Config {
  NETEASE_COOKIE: string;
  PORT: number;
  DEBUG: boolean;
  SEARCH_LIMIT: number;
  DOWNLOAD_CONCURRENCY: number;
}

// 加载配置
function loadConfig(): Config {
  const config: Config = {
    NETEASE_COOKIE: Deno.env.get('NETEASE_COOKIE') || '',
    PORT: parseInt(Deno.env.get('PORT') || (isDenoDeploy ? '8000' : '3004')),
    DEBUG: (Deno.env.get('DEBUG') || 'false').toLowerCase() === 'true',
    SEARCH_LIMIT: parseInt(Deno.env.get('SEARCH_LIMIT') || '50'),
    DOWNLOAD_CONCURRENCY: parseInt(Deno.env.get('DOWNLOAD_CONCURRENCY') || '3')
  };

  // 验证Cookie - 但不在启动时抛出错误
  if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
    console.warn('⚠️ 警告：未配置有效的NETEASE_COOKIE环境变量');
    if (isDenoDeploy) {
      console.log('🌐 请在Deno Deploy控制台设置环境变量 NETEASE_COOKIE');
    } else {
      console.log('💻 请设置环境变量：export NETEASE_COOKIE="your_cookie"');
    }
    console.log('🔧 Cookie格式示例：MUSIC_U=your_music_u_value;os=pc;appver=8.9.75;');
    console.log('📝 服务将启动，但API调用将返回错误直到设置有效Cookie');
  }

  return config;
}

const config = loadConfig();
const api = new NeteaseEAPI();

// 静态文件服务
async function serveStaticFile(pathname: string): Promise<Response | null> {
  try {
    let filePath = '';
    
    // 路由映射
    if (pathname === '/' || pathname === '/index.html') {
      filePath = './index.html';
    } else if (pathname.startsWith('/css/')) {
      filePath = `.${pathname}`;
    } else if (pathname.startsWith('/js/')) {
      filePath = `.${pathname}`;
    } else {
      return null; // 不是静态文件
    }

    const file = await Deno.readFile(filePath);
    
    // 确定MIME类型
    let contentType = 'text/plain';
    if (filePath.endsWith('.html')) {
      contentType = 'text/html; charset=utf-8';
    } else if (filePath.endsWith('.css')) {
      contentType = 'text/css; charset=utf-8';
    } else if (filePath.endsWith('.js')) {
      contentType = 'application/javascript; charset=utf-8';
    }

    return new Response(file, {
      headers: { 'Content-Type': contentType }
    });
  } catch (error) {
    console.error(`静态文件服务错误: ${pathname}`, error);
    return null;
  }
}

// 短链接解析
async function resolveShortLink(shortCode: string): Promise<string | null> {
  try {
    // 构造短链接URL
    const shortUrl = `http://163cn.tv/${shortCode}`;
    
    // 发送HEAD请求获取重定向
    const response = await fetch(shortUrl, {
      method: 'HEAD',
      redirect: 'manual'
    });

    const location = response.headers.get('location');
    if (location) {
      // 从重定向URL中提取歌曲ID
      const match = location.match(/id=(\d+)/);
      if (match) {
        return match[1];
      }
    }

    return null;
  } catch (error) {
    console.error('短链接解析失败:', error);
    return null;
  }
}

// 请求处理函数
async function handler(req: Request): Promise<Response> {
  const url = new URL(req.url);
  const pathname = url.pathname;

  // CORS头
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    // 静态文件服务
    const staticResponse = await serveStaticFile(pathname);
    if (staticResponse) {
      return new Response(staticResponse.body, {
        headers: { ...Object.fromEntries(staticResponse.headers), ...corsHeaders }
      });
    }

    // API路由处理
    if (pathname === '/api/song' && req.method === 'POST') {
      // 单曲解析
      let body: any;
      try {
        body = await req.json();
        console.log('🎵 收到单曲解析请求体:', body);
      } catch (error) {
        console.error('单曲API JSON解析失败:', error);
        return new Response(JSON.stringify({
          status: 400,
          error: 'JSON格式错误'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }
      let { ids, url: songUrl, level = 'lossless' } = body;
      let songId = ids || songUrl;

      if (!songId) {
        return new Response(JSON.stringify({
          status: 400,
          error: '必须提供歌曲ID或短链接'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      // 如果是短链接代码，先解析
      if (typeof songId === 'string' && songId.length < 10 && !/^\d+$/.test(songId)) {
        console.log(`🔗 解析短链接: ${songId}`);
        const resolvedId = await resolveShortLink(songId);
        if (resolvedId) {
          songId = resolvedId;
          console.log(`✅ 短链接解析成功: ${songId}`);
        } else {
          return new Response(JSON.stringify({
            status: 400,
            error: '短链接解析失败'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }
      }

      console.log(`🎵 [${isDenoDeploy ? 'Deploy' : 'Local'}] 解析歌曲: ${songId}, 音质: ${level}`);

      // 检查Cookie是否有效
      if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
        return new Response(JSON.stringify({
          status: 400,
          error: '服务器未配置有效的NETEASE_COOKIE环境变量，请联系管理员设置'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      // 测试Cookie有效性
      console.log('🍪 测试Cookie有效性...');
      const cookieValid = await api.testCookie(cookies);
      console.log('🍪 Cookie测试结果:', cookieValid ? '✅ 有效' : '❌ 无效');

      const result = await api.url_v1(songId, level as QualityLevel, cookies);

      if (!result?.data?.[0]) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲信息'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const songData = result.data[0];

      if (!songData.url) {
        return new Response(JSON.stringify({
          status: 400,
          error: '无法获取歌曲URL，可能是版权限制或需要会员权限'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      // 获取歌曲详情
      let songInfo: any = {};
      try {
        songInfo = await api.getSongDetail(songId);
      } catch (error) {
        console.warn('获取歌曲详情失败:', error.message);
      }

      const responseData: SongInfo = {
        status: 200,
        name: songInfo?.name || `歌曲ID: ${songId}`,
        ar_name: songInfo?.ar?.map((artist: any) => artist.name).join('/') || '网易云音乐',
        al_name: songInfo?.al?.name || '专辑信息',
        level: api.getQualityName(level as QualityLevel),
        size: api.formatFileSize(songData.size),
        url: songData.url.replace('http://', 'https://'),
        br: songData.br,
        pic: songInfo?.al?.picUrl || '',
        debug: {
          requestedLevel: level,
          actualBr: songData.br,
          isLossless: api.isLosslessQuality(songData.br),
          environment: isDenoDeploy ? 'Deno Deploy' : 'Local'
        }
      };

      if (level === 'lossless' && api.isLosslessQuality(songData.br)) {
        responseData.note = `🎉 成功获取无损音质! 比特率: ${songData.br}`;
      } else if (level === 'lossless') {
        responseData.note = `⚠️ 请求无损但获取到较低音质，比特率: ${songData.br || '未知'}`;
      }

      return new Response(JSON.stringify(responseData), {
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });

    } else if (pathname === '/api/playlist-info' && req.method === 'POST') {
      // 获取歌单/专辑信息
      let body: any;
      try {
        body = await req.json();
        console.log('📋 收到请求体:', body);
      } catch (error) {
        console.error('JSON解析失败:', error);
        return new Response(JSON.stringify({
          status: 400,
          error: 'JSON格式错误'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }
      const { type, id } = body;

      console.log(`📋 [${isDenoDeploy ? 'Deploy' : 'Local'}] 获取歌单信息: 类型=${type}, ID=${id}`);

      // 检查Cookie是否有效
      if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
        return new Response(JSON.stringify({
          status: 400,
          error: '服务器未配置有效的NETEASE_COOKIE环境变量，请联系管理员设置'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        let result: any;

        if (type === 'album') {
          result = await api.getAlbumInfo(id, cookies);
        } else if (type === 'playlist') {
          result = await api.getPlaylistInfo(id, cookies);
        } else {
          throw new Error('无效的类型参数');
        }

        return new Response(JSON.stringify({
          status: 200,
          ...result
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          status: 500,
          error: `获取歌单信息失败: ${error.message}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else if (pathname === '/api/cookie-check' && req.method === 'POST') {
      // Cookie有效性和会员状态检测
      console.log(`🍪 [${isDenoDeploy ? 'Deploy' : 'Local'}] 检测Cookie和会员状态`);

      // 检查Cookie是否有效
      if (!config.NETEASE_COOKIE || !config.NETEASE_COOKIE.includes('MUSIC_U=')) {
        return new Response(JSON.stringify({
          status: 200,
          valid: false,
          error: '服务器未配置有效的NETEASE_COOKIE环境变量'
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      const parsedCookies = api.parseCookie(config.NETEASE_COOKIE);
      const cookies = api.createFullCookieObject(parsedCookies);

      try {
        const isValid = await api.testCookie(cookies);

        if (!isValid) {
          return new Response(JSON.stringify({
            status: 200,
            valid: false,
            error: 'Cookie已失效'
          }), {
            headers: { 'Content-Type': 'application/json', ...corsHeaders }
          });
        }

        // 获取用户信息和会员状态
        const userInfo = await api.getUserInfo(cookies);
        const profile = userInfo?.profile || {};
        const vipInfo = profile.vipType || 0;

        // 尝试多个可能的VIP过期时间字段
        let vipExpireTime = 0;
        if (profile.vipExpireTime && profile.vipExpireTime > 0) {
          vipExpireTime = profile.vipExpireTime;
        } else if (profile.viptypeVersion && profile.viptypeVersion > 0) {
          vipExpireTime = profile.viptypeVersion;
        }

        // 计算会员剩余天数
        const now = Date.now();
        let vipDaysLeft = 0;
        let vipExpired = true;

        if (vipExpireTime > 0) {
          vipDaysLeft = vipExpireTime > now ? Math.ceil((vipExpireTime - now) / (1000 * 60 * 60 * 24)) : 0;
          vipExpired = vipExpireTime <= now;
        }

        // 智能判断VIP状态
        const isVip = vipInfo > 0;
        if (!isVip) {
          // 非会员用户
        } else if (vipExpireTime === 0 || vipInfo >= 11) {
          // SVIP用户或长期有效
          vipExpired = false;
          vipDaysLeft = 999;
        }

        return new Response(JSON.stringify({
          status: 200,
          valid: true,
          vipType: vipInfo,
          vipExpired: vipExpired,
          vipDaysLeft: vipDaysLeft,
          vipExpireTime: vipExpireTime > 0 ? new Date(vipExpireTime).toLocaleDateString('zh-CN') : '长期有效',
          isVip: isVip
        }), {
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      } catch (error) {
        console.error('Cookie检测失败:', error);
        return new Response(JSON.stringify({
          status: 500,
          valid: false,
          error: error.message
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

    } else {
      // 404 Not Found
      return new Response('Not Found', { 
        status: 404,
        headers: corsHeaders
      });
    }
  } catch (error) {
    console.error('请求处理错误:', error);
    return new Response(JSON.stringify({
      status: 500,
      error: '服务器内部错误'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 启动信息
console.log(`🦕 ${isDenoDeploy ? 'Deno Deploy' : 'Deno'} 网易云音乐解析器 - 重构版启动`);
console.log(`🌐 环境: ${isDenoDeploy ? 'Deno Deploy' : '本地环境'}`);
console.log(`🔍 Cookie状态: ${config.NETEASE_COOKIE && config.NETEASE_COOKIE.includes('MUSIC_U=') ? '✅ 从环境变量加载' : '❌ 环境变量未设置'}`);
console.log(`🎵 支持功能: 单曲解析、批量下载、状态检测`);

// 根据环境启动服务
if (isDenoDeploy) {
  // Deno Deploy环境 - 导出处理器
  console.log('🌐 Deno Deploy模式启动');
} else {
  // 本地环境
  console.log(`Listening on http://localhost:${config.PORT}/ (http://localhost:${config.PORT}/)`);
  console.log(`📡 本地服务地址: http://localhost:${config.PORT}`);
  Deno.serve({ port: config.PORT }, handler);
}

// 为Deno Deploy导出默认处理器
export default { fetch: handler };
