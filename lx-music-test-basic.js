/**
 * @name 基础测试源
 * @description 测试脚本是否被正确加载
 * @version 1.0.0
 * <AUTHOR>
 */

// 立即输出，确认脚本开始执行
console.log('========== 脚本开始执行 ==========')
console.log('时间:', new Date().toLocaleString())

// 检查环境
console.log('检查 globalThis.lx:', typeof globalThis.lx)
if (globalThis.lx) {
  console.log('LX API 可用')
  console.log('version:', globalThis.lx.version)
  console.log('env:', globalThis.lx.env)
  console.log('EVENT_NAMES:', globalThis.lx.EVENT_NAMES)
} else {
  console.error('❌ globalThis.lx 不存在！')
  throw new Error('globalThis.lx 不存在')
}

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('API 解构成功')

// 最简单的事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('========== 收到请求 ==========')
  console.log('source:', source)
  console.log('action:', action)
  console.log('info:', info)
  
  if (source === 'wy' && action === 'musicUrl') {
    console.log('处理网易云音乐URL请求')
    // 返回一个固定的测试URL
    return Promise.resolve('https://music.163.com/song/media/outer/url?id=1901371647.mp3')
  }
  
  console.log('不支持的请求')
  return Promise.reject(new Error('不支持'))
})

console.log('事件处理器注册完成')

// 发送初始化事件
console.log('准备发送初始化事件...')

try {
  send(EVENT_NAMES.inited, {
    openDevTools: true,
    sources: {
      wy: {
        name: '基础测试源',
        type: 'music',
        actions: ['musicUrl'],
        qualitys: ['128k', '320k']
      }
    }
  })
  console.log('✅ 初始化事件发送成功')
} catch (error) {
  console.error('❌ 初始化事件发送失败:', error)
  throw error
}

console.log('========== 脚本初始化完成 ==========')
console.log('如果看到这条消息，说明脚本加载成功')
console.log('现在请尝试在落雪音乐中播放歌曲')

// 定时输出，确认脚本持续运行
let counter = 0
setInterval(() => {
  counter++
  console.log(`[心跳 ${counter}] 脚本正在运行...`)
}, 10000) // 每10秒输出一次
