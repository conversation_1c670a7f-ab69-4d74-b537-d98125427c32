/**
 * @name 偷听音乐源
 * @description 基于网易云音乐API的高品质音源，支持无损音质
 * @version 1.0.0
 * <AUTHOR> 4.0 sonnet
 * @homepage https://suwyy.deno.dev
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

// 音质映射：落雪音乐 -> 偷听API (优化版)
// 注意：您的API支持更多音质(sky/jyeffect/jymaster)，但落雪音乐只支持4种
const qualityMap = {
  '128k': 'standard',      // 标准音质
  '320k': 'lossless',      // 320k映射到无损音质 (提升音质)
  'flac': 'hires',         // 无损映射到Hi-Res音质 (进一步提升)
  'flac24bit': 'jymaster'  // 超清母带 (使用您API的最高音质)
}

// API基础配置
const API_BASE = 'https://suwyy.deno.dev'
const API_TIMEOUT = 15000

/**
 * HTTP请求封装
 */
const httpRequest = (url, options = {}) => {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'LX-Music-Custom-Source/1.0.0',
        ...options.headers
      },
      timeout: API_TIMEOUT,
      ...options
    }

    console.log(`[偷听音源] 请求: ${url}`)
    
    request(url, requestOptions, (err, resp) => {
      if (err) {
        console.error(`[偷听音源] 请求失败: ${err.message}`)
        return reject(new Error(`网络请求失败: ${err.message}`))
      }

      try {
        // 修正：安全处理响应体
        let data
        if (typeof resp.body === 'string') {
          data = JSON.parse(resp.body)
        } else {
          data = resp.body
        }

        console.log(`[偷听音源] 响应状态: ${resp.statusCode}`)
        console.log(`[偷听音源] 响应数据:`, data)

        if (resp.statusCode !== 200) {
          throw new Error(`HTTP ${resp.statusCode}: ${data.error || '请求失败'}`)
        }

        resolve(data)
      } catch (parseError) {
        console.error(`[偷听音源] 响应处理失败: ${parseError.message}`)
        console.error(`[偷听音源] 原始响应:`, resp.body)
        reject(new Error(`响应处理失败: ${parseError.message}`))
      }
    })
  })
}

/**
 * 从歌曲信息中提取ID
 */
const extractSongId = (musicInfo) => {
  console.log('[偷听音源] 收到的musicInfo:', musicInfo)

  // 落雪音乐的musicInfo结构可能包含以下字段：
  // - songmid (网易云)
  // - id (通用)
  // - songId (某些情况)
  // - rid (某些平台)

  let songId = musicInfo.songmid || musicInfo.id || musicInfo.songId || musicInfo.rid

  // 如果还是没有，尝试从其他可能的字段获取
  if (!songId && musicInfo.hash) {
    songId = musicInfo.hash
  }

  console.log('[偷听音源] 提取的歌曲ID:', songId)
  return songId
}

/**
 * 验证歌曲ID格式
 */
const validateSongId = (songId) => {
  if (!songId) return false
  
  // 支持纯数字ID和包含字母的ID
  return /^[a-zA-Z0-9]+$/.test(String(songId))
}

/**
 * 获取音乐播放链接
 */
const getMusicUrl = async (musicInfo, quality) => {
  try {
    const songId = extractSongId(musicInfo)
    
    if (!validateSongId(songId)) {
      throw new Error(`无效的歌曲ID: ${songId}`)
    }

    const apiQuality = qualityMap[quality] || 'higher'
    console.log(`[偷听音源] 获取音乐: ID=${songId}, 音质=${quality}->${apiQuality}`)

    const requestData = {
      ids: String(songId),
      level: apiQuality
    }

    const response = await httpRequest(`${API_BASE}/api/song`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })

    console.log(`[偷听音源] API原始响应:`, response)

    // 检查API响应格式 - 您的API直接返回数据，不是嵌套在success字段中
    if (response.status !== 200) {
      throw new Error(response.error || '获取音乐链接失败')
    }

    // 您的API直接返回歌曲数据
    if (!response.url) {
      throw new Error('API返回的音乐数据无效')
    }

    // 验证URL格式
    if (!response.url.startsWith('http')) {
      throw new Error('获取到的音乐链接格式无效')
    }

    console.log(`[偷听音源] 成功获取: ${response.name} - ${response.ar_name}`)
    console.log(`[偷听音源] 音质: ${response.level}, 大小: ${response.size}`)

    // 落雪音乐期望直接返回URL字符串
    return response.url

  } catch (error) {
    console.error(`[偷听音源] 获取音乐失败: ${error.message}`)
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('网络')) {
      throw new Error('网络连接失败，请检查网络设置')
    } else if (error.message.includes('无效')) {
      throw new Error('歌曲信息无效，请重试')
    } else if (error.message.includes('API')) {
      throw new Error('音源服务暂时不可用')
    } else {
      throw error
    }
  }
}

/**
 * API处理器
 */
const apis = {
  wy: {
    musicUrl: getMusicUrl
  }
}

// 注册API请求事件处理器
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log(`[偷听音源] 收到请求: source=${source}, action=${action}`)
  
  try {
    switch (action) {
      case 'musicUrl':
        if (!apis[source] || !apis[source].musicUrl) {
          return Promise.reject(new Error(`不支持的音源: ${source}`))
        }
        
        return apis[source].musicUrl(info.musicInfo, info.type)
          .catch(err => {
            console.error(`[偷听音源] musicUrl处理失败: ${err.message}`)
            return Promise.reject(err)
          })
      
      default:
        return Promise.reject(new Error(`不支持的操作: ${action}`))
    }
  } catch (error) {
    console.error(`[偷听音源] 请求处理异常: ${error.message}`)
    return Promise.reject(error)
  }
})

// 发送初始化完成事件
console.log('[偷听音源] 开始初始化...')

send(EVENT_NAMES.inited, {
  openDevTools: false, // 生产环境关闭开发者工具
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})

console.log('[偷听音源] 初始化完成！支持音质: 128k, 320k, flac, flac24bit')
console.log('[偷听音源] API地址: https://suwyy.deno.dev')
