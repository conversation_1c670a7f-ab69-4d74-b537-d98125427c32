# 🎵 歌单解析功能使用说明

## 📋 功能概述

您的API现在支持两种类型的歌单解析：
1. **普通歌单**：公开的歌单，使用环境变量中的Cookie
2. **我喜欢的歌单**：需要用户登录才能访问，使用落雪音乐的token注入格式

## 🔧 API接口说明

### **接口地址**
```
POST /api/playlist-info
```

### **请求格式**

#### **1. 普通歌单**
```json
{
  "type": "playlist",
  "id": "2213306925"
}
```

#### **2. 我喜欢的歌单（token注入）**
```json
{
  "url": "https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048###0040C48D8B74947D362882BF647D1888CE71C666AF3336A16B3E5878186D4F0AB78B86B743A7301F7B951B0D66251E627CFD79B352C7A375D236233424FA8C54212D2682848B2F635BE6DE85A831F7F9DF94100924E04CBAA477B8ED482553FE81554DA1D3C0388FC91D46AD94549695EF0E5108A858297AC6004678AC4F731A657B3477D656AB1F5CC0086A2C6941783402EB85BC3DECE183E1150193696B7F2B733DFE1ABFA9F0DECEB8838D46A74BBA0A364D78A72F446454792FA4AB58B5D08673A42749D80F50C058BA0EB8A85D53F574F25FBF4CB92856A98BDB599AF104D7224EF8221A536C2660714941E9A785F372D56D63FF77FFAD9DFA93B5EDED584CAABAD1A92D548D8952B6BF145CBA8DC8F1932B25301E8A4F98052A6DE21035283AE3E61045ECA455C6329D6119E7BCA3C7A3E03D0049EB99467595634FE8D908209985370FE9CD46F4672C6666FE517AA20873D93EA10E9DC763461B87E57C"
}
```

## 🎯 落雪音乐Token格式详解

### **格式规范**
```
[歌单URL]###[MUSIC_U Token]
```

### **支持的URL格式**
- 标准格式：`https://music.163.com/#/playlist?id=123456`
- 移动端格式：`https://music.163.com/m/playlist?id=123456&creatorId=789`
- 简化格式：`https://music.163.com/playlist/123456`
- 纯ID格式：`123456`

### **Token获取方法**
1. 浏览器登录网易云音乐
2. 按F12打开开发者工具
3. Application → Cookies → https://music.163.com
4. 找到名称为 `MUSIC_U` 的Cookie值

## 🔄 工作流程

### **普通歌单解析流程**
```
用户输入歌单链接 → 提取歌单ID → 使用环境变量Cookie → 获取歌单信息
```

### **我喜欢的歌单解析流程**
```
用户输入URL###TOKEN → 解析URL和Token → 使用用户Token → 获取歌单信息
```

## 💻 前端使用示例

### **HTML输入框**
```html
<input type="text" id="playlistUrl" placeholder="输入歌单链接或落雪音乐格式">
<button onclick="parsePlaylist()">解析歌单</button>
```

### **JavaScript调用**
```javascript
// 自动检测格式并调用相应的解析方法
async function parsePlaylist() {
    const playlistUrl = document.getElementById('playlistUrl').value.trim();
    
    let requestBody;
    if (playlistUrl.includes('###')) {
        // 落雪音乐token格式
        requestBody = { url: playlistUrl };
    } else {
        // 普通歌单格式
        requestBody = { 
            type: 'playlist', 
            id: extractPlaylistId(playlistUrl) 
        };
    }
    
    const response = await fetch('/api/playlist-info', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
    });
    
    const data = await response.json();
    // 处理返回结果...
}
```

## 📊 响应格式

### **成功响应**
```json
{
  "status": 200,
  "name": "我喜欢的音乐",
  "description": "歌单描述",
  "songs": [
    {
      "id": 123456,
      "name": "歌曲名",
      "artist": "歌手名",
      "album": "专辑名"
    }
  ],
  "total": 100
}
```

### **错误响应**
```json
{
  "status": 400,
  "error": "未提供有效的认证信息。对于"我喜欢的歌单"，请使用格式：URL###token"
}
```

## 🛡️ 安全注意事项

### **Token安全**
- MUSIC_U Token是用户的身份令牌，请妥善保管
- 不要在公共场所或不安全的网络环境下输入Token
- Token有时效性，过期后需要重新获取

### **隐私保护**
- 您的API不会存储用户的Token
- Token仅用于当次请求，不会被记录或缓存

## 🔧 故障排除

### **常见错误及解决方案**

1. **"未提供有效的认证信息"**
   - 检查环境变量NETEASE_COOKIE是否配置
   - 对于"我喜欢的歌单"，确保使用URL###TOKEN格式

2. **"无法解析歌单ID"**
   - 检查URL格式是否正确
   - 确保包含歌单ID信息

3. **"Cookie已失效"**
   - 重新获取MUSIC_U Token
   - 检查Token格式是否完整

## 🎉 使用示例

### **普通歌单**
```
输入：https://music.163.com/playlist?id=2213306925
结果：使用环境变量Cookie获取歌单信息
```

### **我喜欢的歌单**
```
输入：https://music.163.com/m/playlist?id=2213306925&creatorId=1448699048###[您的MUSIC_U值]
结果：使用用户Token获取私人歌单信息
```

## 📝 总结

- ✅ 支持普通歌单和私人歌单
- ✅ 兼容落雪音乐的token注入格式
- ✅ 自动检测输入格式
- ✅ 安全的Token处理机制
- ✅ 详细的错误提示

现在您可以轻松解析任何网易云音乐歌单，包括需要登录才能访问的"我喜欢的歌单"！
