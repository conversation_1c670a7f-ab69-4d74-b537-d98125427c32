/**
 * @name 偷听音乐源(诊断版)
 * @description 专门用于诊断URL问题
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[诊断] 脚本启动，开始诊断模式')

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// 测试URL的有效性
const testUrl = (url) => {
  console.log('[诊断] 开始测试URL有效性:', url)
  
  return new Promise((resolve) => {
    request(url, {
      method: 'HEAD',
      timeout: 5000,
      headers: {
        'User-Agent': 'LX-Music-Desktop'
      }
    }, (err, resp) => {
      if (err) {
        console.log('[诊断] URL测试失败:', err.message)
        resolve(false)
      } else {
        console.log('[诊断] URL测试成功，状态码:', resp.statusCode)
        console.log('[诊断] 响应头:', resp.headers)
        resolve(resp.statusCode === 200)
      }
    })
  })
}

const apis = {
  wy: {
    musicUrl({ songmid, id, songId, rid }, quality) {
      console.log('[诊断] ========== 开始处理音乐URL请求 ==========')
      console.log('[诊断] 接收到的参数:')
      console.log('[诊断]   songmid:', songmid)
      console.log('[诊断]   id:', id)
      console.log('[诊断]   songId:', songId)
      console.log('[诊断]   rid:', rid)
      console.log('[诊断]   quality:', quality)
      
      // 提取歌曲ID
      const songIdValue = songmid || id || songId || rid
      if (!songIdValue) {
        console.error('[诊断] ❌ 无法提取歌曲ID')
        return Promise.reject(new Error('无法提取歌曲ID'))
      }
      
      console.log('[诊断] ✅ 提取到歌曲ID:', songIdValue)
      console.log('[诊断] ✅ 使用音质:', quality)
      
      // 构建请求
      const requestData = {
        ids: String(songIdValue),
        level: quality || 'lossless'
      }
      
      console.log('[诊断] 📤 发送API请求:')
      console.log('[诊断]   URL: https://suwyy.deno.dev/api/song')
      console.log('[诊断]   数据:', JSON.stringify(requestData))
      
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        body: JSON.stringify(requestData),
        timeout: 15000
      }).then(async (responseBody) => {
        console.log('[诊断] 📥 收到API响应:')
        console.log('[诊断]   原始响应:', responseBody)
        
        try {
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          console.log('[诊断] 📊 解析后的数据:')
          console.log('[诊断]   status:', data.status)
          console.log('[诊断]   url:', data.url)
          console.log('[诊断]   name:', data.name)
          console.log('[诊断]   ar_name:', data.ar_name)
          console.log('[诊断]   level:', data.level)
          console.log('[诊断]   size:', data.size)
          console.log('[诊断]   br:', data.br)
          
          if (data && data.status === 200 && data.url) {
            const originalUrl = data.url
            console.log('[诊断] ✅ API返回成功')
            console.log('[诊断] 🔗 原始URL:', originalUrl)
            
            // 分析URL
            console.log('[诊断] 🔍 URL分析:')
            console.log('[诊断]   协议:', originalUrl.startsWith('https://') ? 'HTTPS' : originalUrl.startsWith('http://') ? 'HTTP' : '未知')
            console.log('[诊断]   域名:', originalUrl.split('/')[2])
            console.log('[诊断]   路径:', originalUrl.split('/').slice(3).join('/'))
            
            // 测试URL有效性
            console.log('[诊断] 🧪 开始测试URL有效性...')
            const isValid = await testUrl(originalUrl)
            console.log('[诊断] 🧪 URL有效性测试结果:', isValid ? '✅ 有效' : '❌ 无效')
            
            // 如果是HTTP，尝试转换为HTTPS
            let finalUrl = originalUrl
            if (originalUrl.startsWith('http://')) {
              finalUrl = originalUrl.replace('http://', 'https://')
              console.log('[诊断] 🔄 转换为HTTPS:', finalUrl)
              
              const httpsValid = await testUrl(finalUrl)
              console.log('[诊断] 🧪 HTTPS URL测试结果:', httpsValid ? '✅ 有效' : '❌ 无效')
              
              if (!httpsValid) {
                console.log('[诊断] ⚠️ HTTPS无效，使用原始HTTP URL')
                finalUrl = originalUrl
              }
            }
            
            console.log('[诊断] 🎯 最终返回URL:', finalUrl)
            console.log('[诊断] ========== 返回给落雪音乐 ==========')
            
            return finalUrl
            
          } else {
            const error = (data && data.error) || '获取音乐链接失败'
            console.error('[诊断] ❌ API错误:', error)
            console.error('[诊断] 完整响应数据:', data)
            throw new Error(error)
          }
        } catch (parseError) {
          console.error('[诊断] ❌ 响应解析失败:', parseError.message)
          console.error('[诊断] 原始响应体:', responseBody)
          throw new Error('响应解析失败: ' + parseError.message)
        }
      }).catch(error => {
        console.error('[诊断] ❌ 请求失败:', error.message)
        console.error('[诊断] 错误类型:', error.constructor.name)
        console.error('[诊断] 错误堆栈:', error.stack)
        throw error
      })
    },
  }
}

// 事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[诊断] ========== 收到落雪音乐请求 ==========')
  console.log('[诊断] source:', source)
  console.log('[诊断] action:', action)
  console.log('[诊断] info完整对象:', JSON.stringify(info, null, 2))
  
  switch (action) {
    case 'musicUrl':
      if (!apis[source]) {
        console.error('[诊断] ❌ 不支持的源:', source)
        return Promise.reject(new Error('不支持的源: ' + source))
      }
      
      if (!qualitys[source] || !qualitys[source][info.type]) {
        console.error('[诊断] ❌ 不支持的音质:', info.type)
        console.error('[诊断] 可用音质:', Object.keys(qualitys[source] || {}))
        return Promise.reject(new Error('不支持的音质: ' + info.type))
      }
      
      const quality = qualitys[source][info.type]
      console.log('[诊断] 🎵 音质映射:', info.type, '->', quality)
      console.log('[诊断] 🎵 歌曲信息:', JSON.stringify(info.musicInfo, null, 2))
      
      return apis[source].musicUrl(info.musicInfo, quality).catch(err => {
        console.error('[诊断] ❌ 最终处理失败:', err.message)
        return Promise.reject(err)
      })
    
    default:
      console.error('[诊断] ❌ 不支持的操作:', action)
      return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

// 初始化
console.log('[诊断] 🔧 开始初始化...')

send(EVENT_NAMES.inited, {
  openDevTools: true, // 开启调试工具
  sources: {
    wy: {
      name: '偷听音乐源(诊断版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[诊断] ========== 初始化完成 ==========')
console.log('[诊断] 🔍 诊断模式已启用')
console.log('[诊断] 📊 将提供详细的URL分析和测试')
console.log('[诊断] 🎵 准备接收播放请求...')

// 添加全局错误捕获
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    console.error('[诊断] 全局错误:', event.error)
  })
  
  window.addEventListener('unhandledrejection', (event) => {
    console.error('[诊断] 未处理的Promise拒绝:', event.reason)
  })
}
