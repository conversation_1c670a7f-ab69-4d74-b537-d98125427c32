/**
 * 主逻辑模块 - 连接各个模块，处理主要业务逻辑
 */

/**
 * 切换选项卡
 */
function showTab(tabName) {
    window.uiController.showTab(tabName);
}

/**
 * 自动解析输入内容
 */
function autoParseInput(type) {
    window.uiController.autoParseInput(type);
}

/**
 * 解析单曲
 */
async function parseSingle() {
    const input = document.getElementById('singleInput').value.trim();
    const quality = document.getElementById('singleQuality').value;

    if (!input) {
        window.uiController.showError('请输入单曲分享内容或链接');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    if (!parseResult || parseResult.type !== 'song') {
        window.uiController.showError('无法识别单曲信息，请检查输入内容');
        return;
    }

    const formatted = window.musicParser.formatResult(parseResult);
    if (!formatted) {
        window.uiController.showError('解析失败，请检查输入格式');
        return;
    }

    window.uiController.showLoading('正在解析单曲...');

    try {
        // 直接传递ID给后端，让后端处理短链接解析
        const result = await window.musicAPI.parseSong(formatted.id, quality);

        if (result.success) {
            const data = result.data;
            const extractedInfo = formatted.extractedInfo;

            // 显示解析结果
            window.uiController.showSuccess(`
                <div style="position: relative;">
                    <h3>✅ 解析成功</h3>
                    <div style="position: absolute; top: 0; right: 0; display: flex; gap: 8px;">
                        <button id="playBtn_${formatted.id}"
                                data-url="${data.url}"
                                data-title="${(extractedInfo.songName || data.name).replace(/"/g, '&quot;')}"
                                data-artist="${(extractedInfo.artist || data.ar_name).replace(/"/g, '&quot;')}"
                                data-quality="${data.level}"
                                onclick="playSongFromButton(this)"
                                style="padding: 8px 16px; font-size: 16px; border-radius: 10px; background: linear-gradient(135deg, rgba(255, 107, 157, 0.8), rgba(196, 113, 237, 0.8)); color: white; border: none; cursor: pointer; box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3); transition: all 0.3s ease; font-weight: 600;">
                            试听
                        </button>
                        <button onclick="downloadSingleSong('${formatted.id}', '${quality}')"
                                style="padding: 8px 16px; font-size: 16px; border-radius: 10px; background: linear-gradient(135deg, rgba(18, 194, 233, 0.8), rgba(196, 113, 237, 0.8)); color: white; border: none; cursor: pointer; box-shadow: 0 4px 12px rgba(18, 194, 233, 0.3); transition: all 0.3s ease; font-weight: 600;">
                            下载
                        </button>
                    </div>
                    <p><strong>歌曲：</strong>${extractedInfo.songName || data.name}</p>
                    <p><strong>艺术家：</strong>${extractedInfo.artist || data.ar_name}</p>
                    <p><strong>专辑：</strong>${data.al_name}</p>
                    <p><strong>音质：</strong>${data.level}</p>
                    <p><strong>大小：</strong>${data.size}</p>
                    ${data.note ? `<p><strong>说明：</strong>${data.note}</p>` : ''}
                </div>
            `);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('解析单曲失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 下载单曲
 */
async function downloadSingleSong(songId, quality) {
    window.uiController.showLoading('正在获取下载链接...');

    try {
        const result = await window.musicAPI.parseSong(songId, quality);

        if (result.success && result.data.url) {
            const data = result.data;
            const extension = (quality === 'lossless' || quality === 'hires') ? 'flac' : 'mp3';
            const filename = `${data.ar_name} - ${data.name}`;

            // 下载文件
            await window.musicAPI.downloadFile(result.data.url, filename, quality);

            window.uiController.showSuccess(`
                <h3>✅ 下载开始</h3>
                <p><strong>文件名：</strong>${filename}.${extension}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
            `);
        } else {
            window.uiController.showError('无法获取下载链接');
        }
    } catch (error) {
        console.error('下载失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 加载歌单/专辑列表 - 完全兼容普通歌单和"我喜欢的歌单"token格式
 */
async function loadPlaylist() {
    const type = document.getElementById('batchType').value;
    const input = document.getElementById('batchInput').value.trim();

    if (!input) {
        window.uiController.showError('请输入歌单或专辑分享内容');
        return;
    }

    console.log('🎵 开始加载歌单/专辑:', { type, input: input.substring(0, 100) + '...' });
    window.uiController.showLoading(`正在加载${type === 'playlist' ? '歌单' : '专辑'}信息...`);

    try {
        let requestBody;
        let detectedType = type;

        // 🔑 优先检查是否是落雪音乐的token注入格式 (URL###TOKEN)
        if (input.includes('###')) {
            console.log('🔑 检测到落雪音乐token格式 (URL###TOKEN)');

            // 解析URL和token
            const [urlPart, tokenPart] = input.split('###');
            const cleanUrl = urlPart.trim();
            const userToken = tokenPart.trim();

            console.log('📋 分离结果:', {
                urlPart: cleanUrl,
                tokenLength: userToken.length,
                tokenPreview: userToken.substring(0, 50) + '...'
            });

            // 从URL中提取歌单ID
            const playlistId = extractPlaylistId(cleanUrl);

            if (!playlistId) {
                throw new Error(`无法从URL中解析歌单ID: ${cleanUrl}`);
            }

            if (!userToken || userToken.length < 100) {
                throw new Error(`Token格式不正确，长度: ${userToken.length}，期望 > 100`);
            }

            // 构建包含token的请求体
            requestBody = {
                type: 'playlist',
                id: playlistId,
                token: userToken
            };

            detectedType = 'playlist';
            console.log('🔑 构建私人歌单请求:', { id: playlistId, tokenLength: userToken.length });

        } else {
            // 📋 普通歌单/专辑格式处理
            console.log('📋 处理普通歌单/专辑格式');

            // 尝试智能解析
            const parseResult = window.musicParser.parseInput(input);
            let id = input;

            if (parseResult) {
                const formatted = window.musicParser.formatResult(parseResult);
                if (formatted && (formatted.type === 'playlist' || formatted.type === 'album')) {
                    id = formatted.id;
                    detectedType = formatted.type;

                    // 自动更新类型选择
                    document.getElementById('batchType').value = detectedType;
                    console.log('📋 智能解析成功:', { type: detectedType, id });
                }
            } else {
                // 如果智能解析失败，尝试直接提取ID
                const directId = extractPlaylistId(input);
                if (directId) {
                    id = directId;
                    console.log('📋 直接提取ID成功:', id);
                }
            }

            requestBody = {
                type: detectedType,
                id: id
            };

            console.log('📋 构建公开歌单/专辑请求:', requestBody);
        }

        console.log('📡 发送API请求:', requestBody);

        // 直接调用API
        const response = await fetch('/api/playlist-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
        });

        console.log('📡 API响应状态:', response.status);
        const data = await response.json();
        console.log('📡 API响应数据:', data);

        if (data.status === 200) {
            console.log('✅ 加载成功:', {
                name: data.name,
                songCount: data.songs?.length || 0,
                type: detectedType
            });
            window.uiController.showPlaylist(data);
        } else {
            console.error('❌ API返回错误:', data.error);
            throw new Error(data.error);
        }
    } catch (error) {
        console.error('❌ 加载失败:', error);
        window.uiController.showError(`加载失败: ${error.message}`);
    } finally {
        window.uiController.hideLoading();
    }
}

/**
 * 切换全选状态
 */
function toggleSelectAll() {
    window.uiController.toggleSelectAll();
}

/**
 * 下载选中的歌曲
 */
async function downloadSelected() {
    const selectedSongs = window.uiController.getSelectedSongs();
    
    if (selectedSongs.length === 0) {
        window.uiController.showError('请选择要下载的歌曲');
        return;
    }

    const quality = document.getElementById('batchQuality').value;

    try {
        const results = await window.musicAPI.batchDownload(selectedSongs, quality);
        window.uiController.showDownloadComplete(results);
    } catch (error) {
        console.error('批量下载失败:', error);
        window.uiController.showError(error.message);
    }
}



/**
 * 切换歌曲选择状态
 */
function toggleSongSelection(songId) {
    const checkbox = document.getElementById(`song_${songId}`);

    if (checkbox) {
        checkbox.checked = !checkbox.checked;
    }
}

/**
 * 音乐播放器相关功能
 */
let currentAudio = null;
let isPlaying = false;
let currentSongData = null;

/**
 * 从按钮获取数据并播放歌曲
 */
function playSongFromButton(button) {
    const url = button.dataset.url;
    const title = button.dataset.title;
    const artist = button.dataset.artist;
    const quality = button.dataset.quality;

    playSong(url, title, artist, quality);
}

/**
 * 播放歌曲
 */
function playSong(url, title, artist, quality) {
    // 如果已有播放器，先停止
    if (currentAudio) {
        currentAudio.pause();
        currentAudio = null;
    }

    // 存储当前歌曲信息
    currentSongData = { url, title, artist, quality };

    // 显示播放器
    showMusicPlayer(title, artist, quality);

    // 创建音频对象
    currentAudio = new Audio(url);
    currentAudio.volume = 0.7;

    // 音频事件监听
    currentAudio.addEventListener('loadstart', () => {
        updatePlayerCover('🔄');
    });

    currentAudio.addEventListener('canplay', () => {
        updateDuration();
        updatePlayerCover('🎵');
    });

    currentAudio.addEventListener('timeupdate', () => {
        updateProgress();
    });

    currentAudio.addEventListener('ended', () => {
        isPlaying = false;
        updatePlayButton();
        updatePlayerCover('✅');
    });

    currentAudio.addEventListener('error', (e) => {
        updatePlayerCover('❌');
        console.error('音频播放错误:', e);
    });

    currentAudio.addEventListener('playing', () => {
        updatePlayerCover('🎵');
    });

    // 开始播放
    playPause();
}

/**
 * 显示音乐播放器
 */
function showMusicPlayer(title, artist, quality) {
    const playerHtml = `
        <div class="music-player" id="musicPlayer">
            <div class="player-info">
                <div class="player-cover">🎵</div>
                <div class="player-details">
                    <div class="player-title" title="${title}">${title}</div>
                    <div class="player-artist" title="${artist}">${artist}</div>
                </div>
                <div class="player-quality">${quality}</div>
            </div>

            <div class="progress-container-player">
                <div class="progress-bar-player" onclick="seekTo(event)" title="点击跳转">
                    <div class="progress-fill-player" id="progressFill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">0:00</span>
                    <span id="totalTime">0:00</span>
                </div>
                <div class="player-controls-bottom">
                    <button class="play-btn" id="playBtn" onclick="playPause()" title="播放/暂停">
                        ▶️
                    </button>
                </div>
            </div>
        </div>
    `;

    // 在结果区域后面插入播放器
    const resultContainer = document.getElementById('result');
    if (resultContainer) {
        // 移除已存在的播放器
        const existingPlayer = document.getElementById('musicPlayer');
        if (existingPlayer) {
            existingPlayer.remove();
        }

        resultContainer.insertAdjacentHTML('afterend', playerHtml);

        // 添加淡入动画
        setTimeout(() => {
            const player = document.getElementById('musicPlayer');
            if (player) {
                player.style.opacity = '0';
                player.style.transform = 'translateY(20px)';
                player.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    player.style.opacity = '1';
                    player.style.transform = 'translateY(0)';
                }, 50);
            }
        }, 10);
    }
}

/**
 * 播放/暂停切换
 */
function playPause() {
    if (!currentAudio) return;

    const playBtn = document.getElementById('playBtn');

    if (isPlaying) {
        currentAudio.pause();
        isPlaying = false;
    } else {
        // 添加加载状态
        if (playBtn) {
            playBtn.textContent = '⏳';
        }

        currentAudio.play().then(() => {
            isPlaying = true;
        }).catch(e => {
            console.error('播放失败:', e);
            isPlaying = false;
        });
    }

    updatePlayButton();
}

/**
 * 更新播放按钮
 */
function updatePlayButton() {
    const playBtn = document.getElementById('playBtn');
    if (playBtn) {
        playBtn.textContent = isPlaying ? '⏸️' : '▶️';
    }
}

/**
 * 更新播放进度
 */
function updateProgress() {
    if (!currentAudio) return;

    const progress = (currentAudio.currentTime / currentAudio.duration) * 100;
    const progressFill = document.getElementById('progressFill');
    const currentTimeEl = document.getElementById('currentTime');

    if (progressFill) {
        progressFill.style.width = `${progress || 0}%`;
    }

    if (currentTimeEl) {
        currentTimeEl.textContent = formatTime(currentAudio.currentTime);
    }
}

/**
 * 更新总时长
 */
function updateDuration() {
    if (!currentAudio) return;

    const totalTimeEl = document.getElementById('totalTime');
    if (totalTimeEl && currentAudio.duration) {
        totalTimeEl.textContent = formatTime(currentAudio.duration);
    }
}

/**
 * 更新播放器封面
 */
function updatePlayerCover(icon) {
    const coverEl = document.querySelector('.player-cover');
    if (coverEl) {
        coverEl.textContent = icon;
        // 添加封面变化动画
        coverEl.style.transform = 'scale(1.1) rotate(10deg)';
        setTimeout(() => {
            coverEl.style.transform = 'scale(1) rotate(0deg)';
        }, 300);
    }
}

/**
 * 格式化时间
 */
function formatTime(seconds) {
    if (isNaN(seconds)) return '0:00';

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}



/**
 * 跳转到指定位置
 */
function seekTo(event) {
    if (!currentAudio || !currentAudio.duration) return;

    const progressBar = event.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;

    currentAudio.currentTime = percentage * currentAudio.duration;
}

/**
 * 文学引用轮播数据
 */
const literaryQuotes = [
    {
        text: "最难过的是看到你和我在一起，你却显得如此孤单",
        author: "——《偷影子的人》"
    },
    {
        text: "没有人喜欢孤独，只是不想勉强交朋友。",
        author: "—— 村上春树"
    },
    {
        text: "南去北来休便休，白蘋吹尽楚江秋。",
        author: "——《提淮南寺》"
    },
    {
        text: "厌倦是因为你停止了成长。",
        author: "——尼采"
    },
    {
        text: "生命只是个诺言，别为它悲伤。",
        author: "——北岛"
    },
    {
        text: "只要我能拥抱这世界，那拥抱得笨拙又有什么关系。",
        author: "——加缪"
    },
    {
        text: "永恒只是一瞬间，刚够开一个玩笑。",
        author: "——黑塞"
    }
];

let currentQuoteIndex = 0;

/**
 * 切换到下一条引用
 */
function nextQuote() {
    const quoteText = document.getElementById('quoteText');
    const quoteAuthor = document.getElementById('quoteAuthor');

    if (!quoteText || !quoteAuthor) return;

    // 淡出当前引用
    quoteText.classList.add('fade-out');
    quoteAuthor.classList.add('fade-out');

    setTimeout(() => {
        // 更新到下一条引用
        currentQuoteIndex = (currentQuoteIndex + 1) % literaryQuotes.length;
        const currentQuote = literaryQuotes[currentQuoteIndex];

        quoteText.textContent = currentQuote.text;
        quoteAuthor.textContent = currentQuote.author;

        // 移除淡出效果，添加淡入效果
        quoteText.classList.remove('fade-out');
        quoteAuthor.classList.remove('fade-out');
        quoteText.classList.add('fade-in');
        quoteAuthor.classList.add('fade-in');

        // 清理淡入类
        setTimeout(() => {
            quoteText.classList.remove('fade-in');
            quoteAuthor.classList.remove('fade-in');
        }, 800);
    }, 400);
}

/**
 * 初始化引用轮播
 */
function initQuoteCarousel() {
    // 每8秒切换一次引用
    setInterval(nextQuote, 8000);
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 偷听已加载');

    // 初始化默认选项卡
    showTab('single');

    // 初始化引用轮播
    initQuoteCarousel();

    // 检查所有模块是否正确加载
    if (!window.musicAPI) {
        console.error('❌ API模块加载失败');
    }
    if (!window.musicParser) {
        console.error('❌ 解析模块加载失败');
    }
    if (!window.uiController) {
        console.error('❌ UI控制模块加载失败');
    }

    console.log('✅ 所有模块加载完成');
});



/**
 * 从URL中提取歌单ID
 */
function extractPlaylistId(url) {
    // 支持多种URL格式
    const patterns = [
        /playlist\?id=(\d+)/,           // 标准格式
        /m\/playlist\?id=(\d+)/,        // 移动端格式
        /playlist\/(\d+)/               // 简化格式
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            return match[1];
        }
    }

    // 如果是纯数字，直接作为歌单ID
    if (/^\d+$/.test(url)) {
        return url;
    }

    return null;
}
