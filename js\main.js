/**
 * 主逻辑模块 - 连接各个模块，处理主要业务逻辑
 */

/**
 * 切换选项卡
 */
function showTab(tabName) {
    window.uiController.showTab(tabName);
}

/**
 * 自动解析输入内容
 */
function autoParseInput(type) {
    window.uiController.autoParseInput(type);
}

/**
 * 解析单曲
 */
async function parseSingle() {
    const input = document.getElementById('singleInput').value.trim();
    const quality = document.getElementById('singleQuality').value;

    if (!input) {
        window.uiController.showError('请输入单曲分享内容或链接');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    if (!parseResult || parseResult.type !== 'song') {
        window.uiController.showError('无法识别单曲信息，请检查输入内容');
        return;
    }

    const formatted = window.musicParser.formatResult(parseResult);
    if (!formatted) {
        window.uiController.showError('解析失败，请检查输入格式');
        return;
    }

    window.uiController.showLoading('正在解析单曲...');

    try {
        // 直接传递ID给后端，让后端处理短链接解析
        const result = await window.musicAPI.parseSong(formatted.id, quality);

        if (result.success) {
            const data = result.data;
            const extractedInfo = formatted.extractedInfo;

            // 显示解析结果
            window.uiController.showSuccess(`
                <h3>✅ 解析成功</h3>
                <p><strong>歌曲：</strong>${extractedInfo.songName || data.name}</p>
                <p><strong>艺术家：</strong>${extractedInfo.artist || data.ar_name}</p>
                <p><strong>专辑：</strong>${data.al_name}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
                ${data.note ? `<p><strong>说明：</strong>${data.note}</p>` : ''}
                <div style="display: flex; gap: 10px; margin-top: 15px; flex-wrap: wrap;">
                    <button id="playBtn_${formatted.id}"
                            data-url="${data.url}"
                            data-title="${(extractedInfo.songName || data.name).replace(/"/g, '&quot;')}"
                            data-artist="${(extractedInfo.artist || data.ar_name).replace(/"/g, '&quot;')}"
                            data-quality="${data.level}"
                            onclick="playSongFromButton(this)"
                            style="flex: 1; min-width: 120px;">
                        🎧 试听歌曲
                    </button>
                    <button onclick="downloadSingleSong('${formatted.id}', '${quality}')" style="flex: 1; min-width: 120px;">
                        🎵 下载歌曲
                    </button>
                </div>
            `);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('解析单曲失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 下载单曲
 */
async function downloadSingleSong(songId, quality) {
    window.uiController.showLoading('正在获取下载链接...');

    try {
        const result = await window.musicAPI.parseSong(songId, quality);

        if (result.success && result.data.url) {
            const data = result.data;
            const extension = (quality === 'lossless' || quality === 'hires') ? 'flac' : 'mp3';
            const filename = `${data.ar_name} - ${data.name}`;

            // 下载文件
            await window.musicAPI.downloadFile(result.data.url, filename, quality);

            window.uiController.showSuccess(`
                <h3>✅ 下载开始</h3>
                <p><strong>文件名：</strong>${filename}.${extension}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
            `);
        } else {
            window.uiController.showError('无法获取下载链接');
        }
    } catch (error) {
        console.error('下载失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 加载歌单/专辑列表
 */
async function loadPlaylist() {
    const type = document.getElementById('batchType').value;
    const input = document.getElementById('batchInput').value.trim();

    if (!input) {
        window.uiController.showError('请输入歌单或专辑分享内容');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    let id = input;
    let detectedType = type;

    if (parseResult) {
        const formatted = window.musicParser.formatResult(parseResult);
        if (formatted && (formatted.type === 'playlist' || formatted.type === 'album')) {
            id = formatted.id;
            detectedType = formatted.type;
            
            // 自动更新类型选择
            document.getElementById('batchType').value = detectedType;
        }
    }

    window.uiController.showLoading(`正在加载${detectedType === 'playlist' ? '歌单' : '专辑'}信息...`);

    try {
        const result = await window.musicAPI.getPlaylistInfo(detectedType, id);

        if (result.success) {
            window.uiController.showPlaylist(result.data);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('加载歌单失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 切换全选状态
 */
function toggleSelectAll() {
    window.uiController.toggleSelectAll();
}

/**
 * 下载选中的歌曲
 */
async function downloadSelected() {
    const selectedSongs = window.uiController.getSelectedSongs();
    
    if (selectedSongs.length === 0) {
        window.uiController.showError('请选择要下载的歌曲');
        return;
    }

    const quality = document.getElementById('batchQuality').value;

    try {
        const results = await window.musicAPI.batchDownload(selectedSongs, quality);
        window.uiController.showDownloadComplete(results);
    } catch (error) {
        console.error('批量下载失败:', error);
        window.uiController.showError(error.message);
    }
}



/**
 * 切换歌曲选择状态
 */
function toggleSongSelection(songId) {
    const checkbox = document.getElementById(`song_${songId}`);

    if (checkbox) {
        checkbox.checked = !checkbox.checked;
    }
}

/**
 * 音乐播放器相关功能
 */
let currentAudio = null;
let isPlaying = false;
let currentSongData = null;

/**
 * 从按钮获取数据并播放歌曲
 */
function playSongFromButton(button) {
    const url = button.dataset.url;
    const title = button.dataset.title;
    const artist = button.dataset.artist;
    const quality = button.dataset.quality;

    playSong(url, title, artist, quality);
}

/**
 * 播放歌曲
 */
function playSong(url, title, artist, quality) {
    // 如果已有播放器，先停止
    if (currentAudio) {
        currentAudio.pause();
        currentAudio = null;
    }

    // 存储当前歌曲信息
    currentSongData = { url, title, artist, quality };

    // 显示播放器
    showMusicPlayer(title, artist, quality);

    // 创建音频对象
    currentAudio = new Audio(url);
    currentAudio.volume = 0.7;

    // 音频事件监听
    currentAudio.addEventListener('loadstart', () => {
        updatePlayerStatus('🔄 加载中...');
        updatePlayerCover('🔄');
    });

    currentAudio.addEventListener('canplay', () => {
        updatePlayerStatus('✅ 准备就绪');
        updateDuration();
        updatePlayerCover('🎵');
    });

    currentAudio.addEventListener('loadeddata', () => {
        updatePlayerStatus('📡 数据加载完成');
    });

    currentAudio.addEventListener('timeupdate', () => {
        updateProgress();
        if (isPlaying) {
            updatePlayerStatus('🎵 正在播放');
        }
    });

    currentAudio.addEventListener('ended', () => {
        isPlaying = false;
        updatePlayButton();
        updatePlayerStatus('🎉 播放完成');
        updatePlayerCover('✅');
    });

    currentAudio.addEventListener('error', (e) => {
        updatePlayerStatus('❌ 播放错误');
        updatePlayerCover('❌');
        console.error('音频播放错误:', e);
    });

    currentAudio.addEventListener('waiting', () => {
        updatePlayerStatus('⏳ 缓冲中...');
    });

    currentAudio.addEventListener('playing', () => {
        updatePlayerStatus('🎵 正在播放');
        updatePlayerCover('🎵');
    });

    currentAudio.addEventListener('pause', () => {
        updatePlayerStatus('⏸️ 已暂停');
    });

    // 开始播放
    playPause();
}

/**
 * 显示音乐播放器
 */
function showMusicPlayer(title, artist, quality) {
    const playerHtml = `
        <div class="music-player" id="musicPlayer">
            <div class="player-info">
                <div class="player-cover">🎵</div>
                <div class="player-details">
                    <div class="player-title" title="${title}">${title}</div>
                    <div class="player-artist" title="${artist}">${artist}</div>
                </div>
                <div class="player-quality">${quality}</div>
            </div>

            <div class="player-controls">
                <button class="play-btn" id="playBtn" onclick="playPause()" title="播放/暂停">
                    ▶️
                </button>
                <div class="volume-control">
                    <span style="color: rgba(255, 255, 255, 0.8); font-size: 16px; margin-right: 5px;">🔊</span>
                    <input type="range" class="volume-slider" id="volumeSlider"
                           min="0" max="100" value="70" onchange="changeVolume(this.value)"
                           title="音量控制">
                </div>
            </div>

            <div class="progress-container-player">
                <div class="progress-bar-player" onclick="seekTo(event)" title="点击跳转">
                    <div class="progress-fill-player" id="progressFill"></div>
                </div>
                <div class="time-display">
                    <span id="currentTime">0:00</span>
                    <span id="playerStatus" style="font-weight: 600; color: rgba(255, 107, 157, 0.9);">准备中...</span>
                    <span id="totalTime">0:00</span>
                </div>
            </div>
        </div>
    `;

    // 在结果区域后面插入播放器
    const resultContainer = document.getElementById('result');
    if (resultContainer) {
        // 移除已存在的播放器
        const existingPlayer = document.getElementById('musicPlayer');
        if (existingPlayer) {
            existingPlayer.remove();
        }

        resultContainer.insertAdjacentHTML('afterend', playerHtml);

        // 添加淡入动画
        setTimeout(() => {
            const player = document.getElementById('musicPlayer');
            if (player) {
                player.style.opacity = '0';
                player.style.transform = 'translateY(20px)';
                player.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    player.style.opacity = '1';
                    player.style.transform = 'translateY(0)';
                }, 50);
            }
        }, 10);
    }
}

/**
 * 播放/暂停切换
 */
function playPause() {
    if (!currentAudio) return;

    const playBtn = document.getElementById('playBtn');

    if (isPlaying) {
        currentAudio.pause();
        isPlaying = false;
        updatePlayerStatus('⏸️ 已暂停');
    } else {
        // 添加加载状态
        if (playBtn) {
            playBtn.textContent = '⏳';
            playBtn.style.transform = 'scale(0.9)';
        }

        currentAudio.play().then(() => {
            isPlaying = true;
            updatePlayerStatus('🎵 正在播放');
        }).catch(e => {
            console.error('播放失败:', e);
            updatePlayerStatus('❌ 播放失败');
            isPlaying = false;
        }).finally(() => {
            if (playBtn) {
                playBtn.style.transform = 'scale(1)';
            }
        });
    }

    updatePlayButton();
}

/**
 * 更新播放按钮
 */
function updatePlayButton() {
    const playBtn = document.getElementById('playBtn');
    if (playBtn) {
        playBtn.textContent = isPlaying ? '⏸️' : '▶️';
    }
}

/**
 * 更新播放进度
 */
function updateProgress() {
    if (!currentAudio) return;

    const progress = (currentAudio.currentTime / currentAudio.duration) * 100;
    const progressFill = document.getElementById('progressFill');
    const currentTimeEl = document.getElementById('currentTime');

    if (progressFill) {
        progressFill.style.width = `${progress || 0}%`;
    }

    if (currentTimeEl) {
        currentTimeEl.textContent = formatTime(currentAudio.currentTime);
    }
}

/**
 * 更新总时长
 */
function updateDuration() {
    if (!currentAudio) return;

    const totalTimeEl = document.getElementById('totalTime');
    if (totalTimeEl && currentAudio.duration) {
        totalTimeEl.textContent = formatTime(currentAudio.duration);
    }
}

/**
 * 更新播放器状态
 */
function updatePlayerStatus(status) {
    const statusEl = document.getElementById('playerStatus');
    if (statusEl) {
        statusEl.textContent = status;
        // 添加状态变化动画
        statusEl.style.transform = 'scale(1.05)';
        setTimeout(() => {
            statusEl.style.transform = 'scale(1)';
        }, 200);
    }
}

/**
 * 更新播放器封面
 */
function updatePlayerCover(icon) {
    const coverEl = document.querySelector('.player-cover');
    if (coverEl) {
        coverEl.textContent = icon;
        // 添加封面变化动画
        coverEl.style.transform = 'scale(1.1) rotate(10deg)';
        setTimeout(() => {
            coverEl.style.transform = 'scale(1) rotate(0deg)';
        }, 300);
    }
}

/**
 * 格式化时间
 */
function formatTime(seconds) {
    if (isNaN(seconds)) return '0:00';

    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 调整音量
 */
function changeVolume(value) {
    if (currentAudio) {
        currentAudio.volume = value / 100;
    }
}

/**
 * 跳转到指定位置
 */
function seekTo(event) {
    if (!currentAudio || !currentAudio.duration) return;

    const progressBar = event.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;

    currentAudio.currentTime = percentage * currentAudio.duration;
}

/**
 * 文学引用轮播数据
 */
const literaryQuotes = [
    {
        text: "最难过的是看到你和我在一起，你却显得如此孤单",
        author: "——《偷影子的人》"
    },
    {
        text: "没有人喜欢孤独，只是不想勉强交朋友。",
        author: "—— 村上春树"
    },
    {
        text: "南去北来休便休，白蘋吹尽楚江秋。",
        author: "——《提淮南寺》"
    },
    {
        text: "厌倦是因为你停止了成长。",
        author: "——尼采"
    },
    {
        text: "生命只是个诺言，别为它悲伤。",
        author: "——北岛"
    },
    {
        text: "只要我能拥抱这世界，那拥抱得笨拙又有什么关系。",
        author: "——加缪"
    },
    {
        text: "永恒只是一瞬间，刚够开一个玩笑。",
        author: "——黑塞"
    }
];

let currentQuoteIndex = 0;

/**
 * 切换到下一条引用
 */
function nextQuote() {
    const quoteText = document.getElementById('quoteText');
    const quoteAuthor = document.getElementById('quoteAuthor');

    if (!quoteText || !quoteAuthor) return;

    // 淡出当前引用
    quoteText.classList.add('fade-out');
    quoteAuthor.classList.add('fade-out');

    setTimeout(() => {
        // 更新到下一条引用
        currentQuoteIndex = (currentQuoteIndex + 1) % literaryQuotes.length;
        const currentQuote = literaryQuotes[currentQuoteIndex];

        quoteText.textContent = currentQuote.text;
        quoteAuthor.textContent = currentQuote.author;

        // 移除淡出效果，添加淡入效果
        quoteText.classList.remove('fade-out');
        quoteAuthor.classList.remove('fade-out');
        quoteText.classList.add('fade-in');
        quoteAuthor.classList.add('fade-in');

        // 清理淡入类
        setTimeout(() => {
            quoteText.classList.remove('fade-in');
            quoteAuthor.classList.remove('fade-in');
        }, 800);
    }, 400);
}

/**
 * 初始化引用轮播
 */
function initQuoteCarousel() {
    // 每8秒切换一次引用
    setInterval(nextQuote, 8000);
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 偷听已加载');

    // 初始化默认选项卡
    showTab('single');

    // 初始化引用轮播
    initQuoteCarousel();

    // 检查所有模块是否正确加载
    if (!window.musicAPI) {
        console.error('❌ API模块加载失败');
    }
    if (!window.musicParser) {
        console.error('❌ 解析模块加载失败');
    }
    if (!window.uiController) {
        console.error('❌ UI控制模块加载失败');
    }

    console.log('✅ 所有模块加载完成');
});
