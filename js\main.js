/**
 * 主逻辑模块 - 连接各个模块，处理主要业务逻辑
 */

/**
 * 切换选项卡
 */
function showTab(tabName) {
    window.uiController.showTab(tabName);
}

/**
 * 自动解析输入内容
 */
function autoParseInput(type) {
    window.uiController.autoParseInput(type);
}

/**
 * 解析单曲
 */
async function parseSingle() {
    const input = document.getElementById('singleInput').value.trim();
    const quality = document.getElementById('singleQuality').value;

    if (!input) {
        window.uiController.showError('请输入单曲分享内容或链接');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    if (!parseResult || parseResult.type !== 'song') {
        window.uiController.showError('无法识别单曲信息，请检查输入内容');
        return;
    }

    const formatted = window.musicParser.formatResult(parseResult);
    if (!formatted) {
        window.uiController.showError('解析失败，请检查输入格式');
        return;
    }

    window.uiController.showLoading('正在解析单曲...');

    try {
        // 直接传递ID给后端，让后端处理短链接解析
        const result = await window.musicAPI.parseSong(formatted.id, quality);

        if (result.success) {
            const data = result.data;
            const extractedInfo = formatted.extractedInfo;
            
            // 显示解析结果
            window.uiController.showSuccess(`
                <h3>✅ 解析成功</h3>
                <p><strong>歌曲：</strong>${extractedInfo.songName || data.name}</p>
                <p><strong>艺术家：</strong>${extractedInfo.artist || data.ar_name}</p>
                <p><strong>专辑：</strong>${data.al_name}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
                ${data.note ? `<p><strong>说明：</strong>${data.note}</p>` : ''}
                <button onclick="downloadSingleSong('${formatted.id}', '${quality}')" style="margin-top: 15px;">
                    🎵 下载歌曲
                </button>
            `);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('解析单曲失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 下载单曲
 */
async function downloadSingleSong(songId, quality) {
    window.uiController.showLoading('正在获取下载链接...');

    try {
        const result = await window.musicAPI.parseSong(songId, quality);

        if (result.success && result.data.url) {
            const data = result.data;
            const extension = (quality === 'lossless' || quality === 'hires') ? 'flac' : 'mp3';
            const filename = `${data.ar_name} - ${data.name}`;

            // 下载文件
            await window.musicAPI.downloadFile(result.data.url, filename, quality);

            window.uiController.showSuccess(`
                <h3>✅ 下载开始</h3>
                <p><strong>文件名：</strong>${filename}.${extension}</p>
                <p><strong>音质：</strong>${data.level}</p>
                <p><strong>大小：</strong>${data.size}</p>
            `);
        } else {
            window.uiController.showError('无法获取下载链接');
        }
    } catch (error) {
        console.error('下载失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 加载歌单/专辑列表
 */
async function loadPlaylist() {
    const type = document.getElementById('batchType').value;
    const input = document.getElementById('batchInput').value.trim();

    if (!input) {
        window.uiController.showError('请输入歌单或专辑分享内容');
        return;
    }

    // 解析输入内容
    const parseResult = window.musicParser.parseInput(input);
    let id = input;
    let detectedType = type;

    if (parseResult) {
        const formatted = window.musicParser.formatResult(parseResult);
        if (formatted && (formatted.type === 'playlist' || formatted.type === 'album')) {
            id = formatted.id;
            detectedType = formatted.type;
            
            // 自动更新类型选择
            document.getElementById('batchType').value = detectedType;
        }
    }

    window.uiController.showLoading(`正在加载${detectedType === 'playlist' ? '歌单' : '专辑'}信息...`);

    try {
        const result = await window.musicAPI.getPlaylistInfo(detectedType, id);

        if (result.success) {
            window.uiController.showPlaylist(result.data);
        } else {
            window.uiController.showError(result.error);
        }
    } catch (error) {
        console.error('加载歌单失败:', error);
        window.uiController.showError(error.message);
    }
}

/**
 * 切换全选状态
 */
function toggleSelectAll() {
    window.uiController.toggleSelectAll();
}

/**
 * 下载选中的歌曲
 */
async function downloadSelected() {
    const selectedSongs = window.uiController.getSelectedSongs();
    
    if (selectedSongs.length === 0) {
        window.uiController.showError('请选择要下载的歌曲');
        return;
    }

    const quality = document.getElementById('batchQuality').value;

    try {
        const results = await window.musicAPI.batchDownload(selectedSongs, quality);
        window.uiController.showDownloadComplete(results);
    } catch (error) {
        console.error('批量下载失败:', error);
        window.uiController.showError(error.message);
    }
}



/**
 * 切换歌曲选择状态
 */
function toggleSongSelection(songId) {
    const checkbox = document.getElementById(`song_${songId}`);
    const playlistItem = checkbox.closest('.playlist-item');

    if (checkbox && playlistItem) {
        checkbox.checked = !checkbox.checked;

        if (checkbox.checked) {
            playlistItem.classList.add('selected');
        } else {
            playlistItem.classList.remove('selected');
        }
    }
}

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎵 网易云音乐解析器 - 重构版已加载');

    // 初始化默认选项卡
    showTab('single');

    // 检查所有模块是否正确加载
    if (!window.musicAPI) {
        console.error('❌ API模块加载失败');
    }
    if (!window.musicParser) {
        console.error('❌ 解析模块加载失败');
    }
    if (!window.uiController) {
        console.error('❌ UI控制模块加载失败');
    }

    console.log('✅ 所有模块加载完成');
});
