# 🎵 落雪音乐自定义源调试指南

## 🔍 问题现状

✅ **API测试正常** - 您的 `https://suwyy.deno.dev` 服务完全正常  
✅ **歌曲解析成功** - 能够正确返回播放URL  
✅ **格式测试通过** - 模拟落雪音乐格式测试成功  
❌ **落雪音乐播放失败** - 在落雪音乐中无法正常播放  

## 🔧 调试步骤

### **步骤1: 检查落雪音乐版本**

不同版本的落雪音乐对自定义源的要求可能不同：

- **桌面版**: 推荐使用最新版本
- **移动版**: 某些功能可能有限制
- **开发版**: 可能有API变更

**操作**: 确认您使用的落雪音乐版本，建议更新到最新版本。

### **步骤2: 正确添加自定义源**

1. **打开落雪音乐**
2. **进入设置** → **自定义源**
3. **添加新源** 或 **编辑现有源**
4. **复制脚本内容**:
   - 使用 `lx-music-source-universal.js` 的完整内容
   - 确保没有遗漏任何字符
5. **保存并启用**

### **步骤3: 检查控制台日志**

在落雪音乐中：

1. **打开开发者工具** (如果支持)
2. **查看控制台** (Console)
3. **搜索歌曲并尝试播放**
4. **观察日志输出**

**期望看到的日志**:
```
[偷听音源] ========== 脚本启动 ==========
[偷听音源] 版本: 3.0.0
[偷听音源] ========== 初始化完成 ==========
[偷听音源] 🎵 准备就绪，等待请求...
```

**播放时的日志**:
```
[偷听音源] ========== 收到请求 ==========
[偷听音源] source: wy
[偷听音源] action: musicUrl
[偷听音源] ========== 开始获取音乐URL ==========
[偷听音源] ✅ 成功获取URL: https://...
```

### **步骤4: 常见问题排查**

#### **问题A: 脚本未加载**
**症状**: 控制台没有任何 `[偷听音源]` 日志  
**解决**: 
- 检查脚本语法是否正确
- 确认脚本已保存并启用
- 重启落雪音乐

#### **问题B: 初始化失败**
**症状**: 看到启动日志但没有"初始化完成"  
**解决**:
- 检查 `globalThis.lx` 是否可用
- 确认落雪音乐版本支持自定义源
- 查看错误信息

#### **问题C: 收不到请求**
**症状**: 初始化成功但播放时没有请求日志  
**解决**:
- 确认选择了正确的音乐源 (网易云音乐)
- 检查自定义源是否已启用
- 尝试搜索不同的歌曲

#### **问题D: 请求失败**
**症状**: 收到请求但获取URL失败  
**解决**:
- 检查网络连接
- 确认API服务状态
- 查看具体错误信息

### **步骤5: 手动测试脚本**

在落雪音乐的控制台中手动执行：

```javascript
// 测试全局对象
console.log('globalThis.lx:', globalThis.lx)

// 测试API请求
globalThis.lx.request('https://suwyy.deno.dev/api/song', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({ids: '1901371647', level: 'lossless'})
}, (err, resp) => {
  console.log('测试结果:', err, resp)
})
```

### **步骤6: 使用简化版本测试**

如果复杂版本有问题，尝试这个最简化的版本：

```javascript
/**
 * @name 测试音乐源
 * @description 最简化测试版本
 * @version 1.0.0
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

on(EVENT_NAMES.request, ({ source, action, info }) => {
  if (source === 'wy' && action === 'musicUrl') {
    return new Promise((resolve, reject) => {
      const songId = info.musicInfo.songmid || info.musicInfo.id || '1901371647'
      const level = info.type === '320k' ? 'lossless' : 'standard'
      
      request('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({ids: songId, level: level})
      }, (err, resp) => {
        if (err) return reject(err)
        const data = JSON.parse(resp.body)
        if (data.status === 200 && data.url) {
          resolve(data.url)
        } else {
          reject(new Error(data.error || '获取失败'))
        }
      })
    })
  }
  return Promise.reject(new Error('不支持'))
})

send(EVENT_NAMES.inited, {
  sources: {
    wy: {
      name: '测试源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k']
    }
  }
})
```

## 🎯 最可能的解决方案

基于您的测试结果，最可能的问题是：

### **1. 落雪音乐版本兼容性**
- 尝试更新到最新版本
- 或者使用稳定版本

### **2. 自定义源配置**
- 确保脚本完整复制
- 检查是否有特殊字符问题
- 重新添加自定义源

### **3. 网络或权限问题**
- 检查落雪音乐的网络权限
- 确认防火墙没有阻止请求
- 尝试使用VPN或更换网络

## 📞 进一步调试

如果以上步骤都无法解决问题，请提供：

1. **落雪音乐版本信息**
2. **控制台完整日志**
3. **错误信息截图**
4. **操作系统信息**

这样我可以提供更精确的解决方案。

---

**🎵 相信通过这些步骤，您的落雪音乐自定义源一定能正常工作！**
