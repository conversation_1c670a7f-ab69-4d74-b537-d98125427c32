/**
 * @name 偷听音乐源
 * @description 完全按照官方示例编写
 * @version 1.0.0
 * <AUTHOR>
 * @homepage https://suwyy.deno.dev
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

const apis = {
  wy: {
    musicUrl({ hash, songmid }, quality) {
      const songId = hash ?? songmid

      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: songId,
          level: quality
        })
      }).then(data => {
        const result = typeof data === 'string' ? JSON.parse(data) : data
        if (result && result.status === 200 && result.url) {
          return result.url
        }
        throw new Error('获取失败')
      })
    },
  }
}

// 完全按照官方示例的事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type]).catch(err => {
        console.log(err)
        return Promise.reject(err)
      })
    default:
      return Promise.reject(new Error('不支持的操作'))
  }
})

// 完全按照官方示例的初始化
send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})
