/**
 * @name 偷听音乐源
 * @description 基于成功音源分析的修正版
 * @version 2.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[偷听音源] 修正版启动')

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

const apis = {
  wy: {
    // 关键修正：使用解构赋值，重点关注hash和songmid字段
    musicUrl({ hash, songmid, id, songId }, quality) {
      console.log('[偷听音源] 开始处理请求')
      console.log('[偷听音源] 参数:', { hash, songmid, id, songId, quality })
      
      // 关键修正：优先使用hash字段，这是成功音源的做法
      const songIdValue = hash || songmid || id || songId || '1901371647'
      console.log('[偷听音源] 使用歌曲ID:', songIdValue)
      console.log('[偷听音源] 使用音质:', quality)
      
      // 方案1：尝试GET方式调用API（模仿成功音源的做法）
      const getUrl = `https://suwyy.deno.dev/api/song/${songIdValue}/${quality}`
      console.log('[偷听音源] 尝试GET方式:', getUrl)
      
      return httpRequest(getUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        timeout: 15000
      }).then(responseBody => {
        console.log('[偷听音源] GET响应:', responseBody)
        
        try {
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          
          if (data && data.status === 200 && data.url) {
            console.log('[偷听音源] ✅ GET方式成功:', data.url)
            return data.url
          } else {
            throw new Error('GET方式失败')
          }
        } catch (error) {
          console.log('[偷听音源] GET方式失败，尝试POST方式')
          
          // 方案2：如果GET失败，回退到POST方式
          return httpRequest('https://suwyy.deno.dev/api/song', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'User-Agent': 'LX-Music-Desktop'
            },
            body: JSON.stringify({
              ids: String(songIdValue),
              level: quality
            }),
            timeout: 15000
          }).then(postResponseBody => {
            console.log('[偷听音源] POST响应:', postResponseBody)
            
            const postData = typeof postResponseBody === 'string' ? JSON.parse(postResponseBody) : postResponseBody
            
            if (postData && postData.status === 200 && postData.url) {
              console.log('[偷听音源] ✅ POST方式成功:', postData.url)
              return postData.url
            } else {
              throw new Error(postData.error || '获取音乐链接失败')
            }
          })
        }
      }).catch(error => {
        console.error('[偷听音源] 所有方式都失败:', error.message)
        throw error
      })
    },
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] 收到请求:', { source, action })
  console.log('[偷听音源] 完整info:', JSON.stringify(info, null, 2))
  
  switch (action) {
    case 'musicUrl':
      const quality = qualitys[source][info.type]
      console.log('[偷听音源] 音质映射:', info.type, '->', quality)
      return apis[source].musicUrl(info.musicInfo, quality)
    default:
      return Promise.reject(new Error('不支持的操作'))
  }
})

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源(修正版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[偷听音源] 修正版初始化完成')
console.log('[偷听音源] 特性: 优先hash字段、GET+POST双重保障')
