/**
 * @name 偷听音乐源
 * @description 严格按照六音成功案例编写
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

// 模仿六音的httpRequest函数
const h = (u, o = { method: 'POST' }) => new Promise((resolve, reject) => {
  request(u, o, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

const apis = {
  wy: {
    // 严格按照六音成功案例：优先使用hash字段
    musicUrl({ hash, songmid }, quality) {
      // 六音成功模式：hash ?? songmid
      const songId = hash ?? songmid

      // 适配您的POST API
      return h('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: songId,
          level: quality
        })
      }).then(data => {
        const result = typeof data === 'string' ? JSON.parse(data) : data
        if (result && result.status === 200 && result.url) {
          return result.url
        }
        throw new Error('获取失败')
      })
    }
  }
}

// 严格按照六音成功案例的事件处理
on(EVENT_NAMES.request, ({ source, action, info }) => {
  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
    default:
      return Promise.reject(new Error('不支持'))
  }
})

// 严格按照六音成功案例的初始化
send(EVENT_NAMES.inited, {
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})
