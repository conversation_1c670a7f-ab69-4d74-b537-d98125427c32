/**
 * @name 偷听音乐源
 * @description 基于成功案例的最终版本
 * @version 1.0.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

// 完全模仿成功案例的请求函数
const h = (u, o = { method: 'GET' }) => new Promise((v, j) => {
  request(u, o, (e, p) => {
    if (e) return j(e)
    v(p)
  })
})

const apis = {
  wy: {
    // 完全按照成功案例的函数签名和逻辑
    musicUrl({ hash, songmid }, quality) {
      // 成功案例的字段优先级
      const songId = hash ?? songmid

      // 适配您的POST API，但保持成功案例的简洁风格
      return h('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ids: songId,
          level: quality
        })
      }).then(data => {
        const result = typeof data === 'string' ? JSON.parse(data) : data
        if (result && result.status === 200 && result.url) {
          return result.url
        }
        throw new Error('获取失败')
      })
    }
  }
}

// 完全模仿成功案例的事件处理（极简版）
on(EVENT_NAMES.request, async ({ source, action, info }) => {
  if (action === 'musicUrl') {
    return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
  }
  return Promise.reject(new Error('不支持'))
})

// 完全模仿成功案例的初始化
send(EVENT_NAMES.inited, {
  sources: {
    wy: {
      name: '偷听音乐源',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit']
    }
  }
})
