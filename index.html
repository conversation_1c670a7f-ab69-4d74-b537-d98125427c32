<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>🎵偷听 </title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- 主容器 -->
    <div class="main-container">
        <div class="container">
            <!-- 头部 -->
            <div class="header">
                <h1>🎵 偷听</h1>
            </div>

            <!-- 文学引用轮播 -->
            <div class="literary-quote" id="literaryQuote">
                <div class="quote-text" id="quoteText">最难过的是看到你和我在一起，你却显得如此孤单</div>
                <div class="quote-author" id="quoteAuthor">——《偷影子的人》</div>
            </div>

            <!-- 选项卡 -->
            <div class="tabs">
                <button class="tab-button active" onclick="showTab('single')">单曲解析</button>
                <button class="tab-button" onclick="showTab('batch')">批量下载</button>
            </div>

            <!-- 单曲解析选项卡 -->
            <div id="single" class="tab-content active">
                <div class="form-section">
                    <div class="form-group">
                        <label for="singleInput">🎵 输入单曲分享内容或链接</label>
                        <input type="text" id="singleInput" 
                               placeholder="粘贴网易云单曲分享内容，如：分享Ni/Co's Covers/Ni/Co的单曲《where is the love?》: http://163cn.tv/Gng7M90"
                               oninput="autoParseInput('single')">
                    </div>
                    <div class="form-group">
                        <label for="singleQuality">🎧 音质选择</label>
                        <select id="singleQuality">
                            <option value="lossless">无损音质 (FLAC)</option>
                            <option value="hires">Hi-Res音质</option>
                            <option value="master">母带音质 (Master)</option>
                        </select>
                    </div>
                    <button onclick="parseSingle()">解析单曲</button>
                </div>
            </div>

            <!-- 批量下载选项卡 -->
            <div id="batch" class="tab-content">
                <div class="form-section">
                    <div class="form-group">
                        <label for="batchType">📋 类型选择</label>
                        <select id="batchType">
                            <option value="playlist">歌单</option>
                            <option value="album">专辑</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="batchInput">📝 输入分享内容或ID</label>
                        <input type="text" id="batchInput"
                               placeholder="粘贴歌单/专辑分享内容，或直接输入ID。我喜欢的歌单请使用：URL###token格式"
                               oninput="autoParseInput('batch')">
                        <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-top: 5px; display: block;">
                            💡 提示：对于"我喜欢的歌单"，请使用落雪音乐提供的格式：<br>
                            <code style="background: rgba(255, 255, 255, 0.1); padding: 2px 4px; border-radius: 3px;">
                                https://music.163.com/playlist?id=123###您的MUSIC_U值
                            </code>
                        </small>
                    </div>
                    <div class="form-group">
                        <label for="batchQuality">🎧 音质选择</label>
                        <select id="batchQuality">
                            <option value="lossless">无损音质 (FLAC)</option>
                            <option value="hires">Hi-Res音质</option>
                            <option value="master">母带音质 (Master)</option>
                        </select>
                    </div>
                    <button onclick="loadPlaylist()">📋 加载歌曲列表</button>
                </div>

                <!-- 歌曲列表容器 -->
                <div id="playlistContainer" class="playlist-container" style="display: none;">
                    <div class="playlist-header">
                        <button onclick="toggleSelectAll()" class="select-all-btn">全选/取消</button>
                        <button onclick="downloadSelected()" class="download-btn">下载选中</button>
                        <small style="color: rgba(255, 255, 255, 0.6); font-size: 12px; margin-left: 10px;">
                            💡 每次最多下载30首歌曲
                        </small>
                    </div>
                    <div id="playlistItems" class="playlist-items"></div>
                </div>
            </div>



            <!-- 结果显示区域 -->
            <div id="result" class="result-container"></div>
        </div>
    </div>

    <!-- 引入JavaScript模块 -->
    <script src="js/api.js"></script>
    <script src="js/parser.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
