# 🔍 落雪音乐自定义源问题分析和修复

## 🎯 发现的关键问题

通过重新仔细研读官方文档，我发现了我们之前脚本的几个严重问题：

### **问题1: 音质映射结构错误**

#### ❌ 我们之前的错误做法：
```javascript
const CONFIG = {
  QUALITY_MAP: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster'
  }
}

// 然后直接使用
const apiQuality = CONFIG.QUALITY_MAP[quality] || 'lossless'
```

#### ✅ 官方规范的正确做法：
```javascript
const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster'
  }
}

// 在事件处理中使用
return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
```

### **问题2: API结构不符合官方规范**

#### ❌ 我们之前的错误做法：
```javascript
function getMusicUrl(musicInfo, quality) {
  // 直接处理逻辑
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  if (source === 'wy' && action === 'musicUrl') {
    return getMusicUrl(info.musicInfo, info.type)
  }
})
```

#### ✅ 官方规范的正确做法：
```javascript
const apis = {
  wy: {
    musicUrl(musicInfo, quality) {
      // 处理逻辑
      return httpRequest('url').then(data => data.url)
    }
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type])
  }
})
```

### **问题3: HTTP请求方式不规范**

#### ❌ 我们之前的错误做法：
```javascript
request(apiUrl, options, (err, resp) => {
  // 直接在回调中处理
})
```

#### ✅ 官方规范的正确做法：
```javascript
const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// 然后在API中使用
return httpRequest('url', options).then(data => {
  // 处理响应
  return data.url
})
```

### **问题4: 事件处理结构不完整**

#### ❌ 我们之前的错误做法：
```javascript
on(EVENT_NAMES.request, ({ source, action, info }) => {
  if (source === 'wy' && action === 'musicUrl') {
    return getMusicUrl(info.musicInfo, info.type)
  }
  return Promise.reject(new Error('不支持'))
})
```

#### ✅ 官方规范的正确做法：
```javascript
on(EVENT_NAMES.request, ({ source, action, info }) => {
  switch (action) {
    case 'musicUrl':
      return apis[source].musicUrl(info.musicInfo, qualitys[source][info.type]).catch(err => {
        console.log(err)
        return Promise.reject(err)
      })
    // 可以添加更多action处理
    default:
      return Promise.reject(new Error(`不支持的操作: ${action}`))
  }
})
```

## 🔧 修复后的完整脚本

新的 `lx-music-source-official.js` 完全按照官方规范编写：

### **1. 正确的结构组织**
- ✅ 按源组织的音质映射 (`qualitys`)
- ✅ 按源组织的API结构 (`apis`)
- ✅ 标准的HTTP请求封装 (`httpRequest`)
- ✅ 规范的事件处理 (`switch` 语句)

### **2. 完全符合官方示例**
- ✅ 变量命名与官方一致
- ✅ 函数结构与官方一致
- ✅ 事件处理与官方一致
- ✅ 初始化格式与官方一致

### **3. 保持您的功能需求**
- ✅ 使用您的API地址 (`https://suwyy.deno.dev`)
- ✅ 保持您的音质映射偏好
- ✅ 保持详细的调试日志
- ✅ 支持网易云音乐 (`wy`)

## 🎯 为什么之前不工作

### **根本原因**
1. **结构不符合规范**: 落雪音乐期望特定的代码结构
2. **音质传递错误**: 我们自己映射了音质，但落雪音乐期望原始音质值传递给API
3. **事件处理不完整**: 缺少标准的switch结构和错误处理
4. **HTTP请求方式**: 没有使用官方推荐的Promise封装方式

### **为什么API测试正常但落雪音乐不工作**
- API本身没问题
- 问题在于落雪音乐调用自定义源的方式与我们的实现不匹配
- 落雪音乐期望特定的函数签名和返回值格式

## 🚀 使用新脚本

1. **复制** `lx-music-source-official.js` 的完整内容
2. **在落雪音乐中添加自定义源**
3. **粘贴脚本内容**
4. **保存并启用**
5. **测试播放**

### **期望的日志输出**
```
[偷听音源] 初始化完成
[偷听音源] 收到请求: {source: "wy", action: "musicUrl", info: {...}}
[偷听音源] 开始获取音乐URL
[偷听音源] ✅ 成功获取URL: https://...
```

## 📊 总结

**问题根源**: 我们没有严格按照官方文档的示例结构编写脚本，而是自己创造了一套结构。

**解决方案**: 完全按照官方示例的结构重写，确保每个部分都符合落雪音乐的期望。

**关键教训**: 在集成第三方系统时，必须严格遵循官方文档的示例，不能随意修改结构。

---

**现在的脚本应该能够正常工作了！** 🎉
