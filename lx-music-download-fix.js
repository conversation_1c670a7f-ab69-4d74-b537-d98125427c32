/**
 * @name 偷听音乐源(下载修复版)
 * @description 解决下载只支持128k的问题
 * @version 2.1.0
 * <AUTHOR>
 */

const { EVENT_NAMES, request, on, send } = globalThis.lx

console.log('[偷听音源] 下载修复版启动')

const qualitys = {
  wy: {
    '128k': 'standard',
    '320k': 'lossless',
    'flac': 'hires',
    'flac24bit': 'jymaster',
  }
}

const httpRequest = (url, options) => new Promise((resolve, reject) => {
  request(url, options, (err, resp) => {
    if (err) return reject(err)
    resolve(resp.body)
  })
})

// 音质验证和调试
function validateQuality(requestedQuality, actualBr) {
  console.log('[偷听音源] 音质验证:')
  console.log('[偷听音源]   请求音质:', requestedQuality)
  console.log('[偷听音源]   实际比特率:', actualBr)
  
  // 根据比特率判断实际音质
  let actualQuality = 'unknown'
  if (actualBr >= 999000) {
    actualQuality = 'lossless/hires'
  } else if (actualBr >= 320000) {
    actualQuality = '320k'
  } else if (actualBr >= 128000) {
    actualQuality = '128k'
  }
  
  console.log('[偷听音源]   实际音质:', actualQuality)
  
  if (requestedQuality === 'lossless' && actualBr < 320000) {
    console.log('[偷听音源] ⚠️ 请求无损但获得低音质，可能是版权限制')
  } else if (requestedQuality === 'hires' && actualBr < 999000) {
    console.log('[偷听音源] ⚠️ 请求Hi-Res但获得低音质，可能是版权限制')
  }
  
  return actualQuality
}

const apis = {
  wy: {
    musicUrl({ hash, songmid, id, songId }, quality) {
      console.log('[偷听音源] ========== 开始处理请求 ==========')
      console.log('[偷听音源] 请求类型: musicUrl (播放/下载)')
      console.log('[偷听音源] 参数:', { hash, songmid, id, songId, quality })
      
      // 优先使用hash字段
      const songIdValue = hash || songmid || id || songId || '1901371647'
      console.log('[偷听音源] 使用歌曲ID:', songIdValue)
      console.log('[偷听音源] 请求音质:', quality)
      
      // 构建请求数据
      const requestData = {
        ids: String(songIdValue),
        level: quality || 'lossless'
      }
      
      console.log('[偷听音源] 发送API请求:', requestData)
      console.log('[偷听音源] API地址: https://suwyy.deno.dev/api/song')
      
      return httpRequest('https://suwyy.deno.dev/api/song', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'User-Agent': 'LX-Music-Desktop'
        },
        body: JSON.stringify(requestData),
        timeout: 20000
      }).then(responseBody => {
        console.log('[偷听音源] 收到API响应:', responseBody)
        
        try {
          const data = typeof responseBody === 'string' ? JSON.parse(responseBody) : responseBody
          console.log('[偷听音源] 解析后数据:', JSON.stringify(data, null, 2))
          
          if (data && data.status === 200 && data.url) {
            console.log('[偷听音源] ✅ API返回成功')
            console.log('[偷听音源] 歌曲信息:', data.name || '未知')
            console.log('[偷听音源] 歌手信息:', data.ar_name || '未知')
            console.log('[偷听音源] 专辑信息:', data.al_name || '未知')
            console.log('[偷听音源] 返回音质:', data.level || '未知')
            console.log('[偷听音源] 文件大小:', data.size || '未知')
            console.log('[偷听音源] 比特率:', data.br || '未知')
            console.log('[偷听音源] 原始URL:', data.url)
            
            // 验证音质
            validateQuality(quality, data.br)
            
            // 确保URL有效
            let finalUrl = data.url
            if (!finalUrl || !finalUrl.startsWith('http')) {
              throw new Error('无效的URL格式: ' + finalUrl)
            }
            
            // 确保使用HTTPS
            if (finalUrl.startsWith('http://')) {
              finalUrl = finalUrl.replace('http://', 'https://')
              console.log('[偷听音源] 转换为HTTPS:', finalUrl)
            }
            
            console.log('[偷听音源] 最终URL:', finalUrl)
            console.log('[偷听音源] 🎵 返回给落雪音乐')
            
            return finalUrl
            
          } else {
            const error = (data && data.error) || '获取音乐链接失败'
            console.error('[偷听音源] ❌ API错误:', error)
            console.error('[偷听音源] 完整响应:', data)
            throw new Error(error)
          }
        } catch (parseError) {
          console.error('[偷听音源] ❌ 响应解析失败:', parseError.message)
          console.error('[偷听音源] 原始响应:', responseBody)
          throw new Error('响应解析失败: ' + parseError.message)
        }
      }).catch(error => {
        console.error('[偷听音源] ❌ 请求失败:', error.message)
        
        // 提供更友好的错误信息
        if (error.message.includes('timeout')) {
          throw new Error('网络超时，请稍后重试')
        } else if (error.message.includes('ENOTFOUND')) {
          throw new Error('无法连接到服务器')
        } else {
          throw error
        }
      })
    },
  }
}

on(EVENT_NAMES.request, ({ source, action, info }) => {
  console.log('[偷听音源] ========== 收到落雪音乐请求 ==========')
  console.log('[偷听音源] source:', source)
  console.log('[偷听音源] action:', action)
  console.log('[偷听音源] info.type (音质):', info.type)
  console.log('[偷听音源] info.musicInfo:', JSON.stringify(info.musicInfo, null, 2))
  
  switch (action) {
    case 'musicUrl':
      if (!apis[source]) {
        console.error('[偷听音源] ❌ 不支持的源:', source)
        return Promise.reject(new Error('不支持的源: ' + source))
      }
      
      if (!qualitys[source] || !qualitys[source][info.type]) {
        console.error('[偷听音源] ❌ 不支持的音质:', info.type)
        console.error('[偷听音源] 可用音质:', Object.keys(qualitys[source] || {}))
        return Promise.reject(new Error('不支持的音质: ' + info.type))
      }
      
      const quality = qualitys[source][info.type]
      console.log('[偷听音源] 🎵 音质映射:', info.type, '->', quality)
      
      return apis[source].musicUrl(info.musicInfo, quality).catch(err => {
        console.error('[偷听音源] ❌ 最终处理失败:', err.message)
        return Promise.reject(err)
      })
    
    default:
      console.error('[偷听音源] ❌ 不支持的操作:', action)
      return Promise.reject(new Error('不支持的操作: ' + action))
  }
})

send(EVENT_NAMES.inited, {
  openDevTools: false,
  sources: {
    wy: {
      name: '偷听音乐源(下载修复版)',
      type: 'music',
      actions: ['musicUrl'],
      qualitys: ['128k', '320k', 'flac', 'flac24bit'],
    }
  }
})

console.log('[偷听音源] ========== 初始化完成 ==========')
console.log('[偷听音源] 版本: 下载修复版 v2.1.0')
console.log('[偷听音源] 支持源: 网易云音乐 (wy)')
console.log('[偷听音源] 支持音质: 128k, 320k, flac, flac24bit')
console.log('[偷听音源] 音质映射:')
console.log('[偷听音源]   128k -> standard (标准音质)')
console.log('[偷听音源]   320k -> lossless (无损音质) ⬆️')
console.log('[偷听音源]   flac -> hires (Hi-Res音质) ⬆️⬆️')
console.log('[偷听音源]   flac24bit -> jymaster (超清母带) ⭐')
console.log('[偷听音源] 特性: 详细音质验证、下载优化、错误诊断')
console.log('[偷听音源] 🎵 准备就绪，等待播放/下载请求...')
